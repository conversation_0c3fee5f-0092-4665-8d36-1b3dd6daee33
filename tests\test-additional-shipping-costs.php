<?php
/**
 * اختبارات رسوم التوصيل الإضافية المباشرة
 * 
 * هذا الملف يحتوي على اختبارات للتأكد من عمل رسوم التوصيل الإضافية بشكل صحيح
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * كلاس اختبار رسوم التوصيل الإضافية
 */
class Form_Elrakami_Additional_Shipping_Costs_Test {

    /**
     * تشغيل جميع الاختبارات
     */
    public static function run_tests() {
        echo "<h2>اختبارات رسوم التوصيل الإضافية المباشرة - Form Elrakami</h2>";
        
        self::test_metabox_functionality();
        self::test_simple_product_additional_costs();
        self::test_variable_product_additional_costs();
        self::test_cost_calculation();
        self::test_integration_with_woocommerce();
        
        echo "<h3>انتهت جميع الاختبارات</h3>";
    }

    /**
     * اختبار وظائف الميتا بوكس
     */
    private static function test_metabox_functionality() {
        echo "<h3>اختبار وظائف الميتا بوكس</h3>";
        
        // التحقق من وجود الكلاس
        if (class_exists('Form_Elrakami_Shipping_Cost_Metabox')) {
            echo "<p style='color: green;'>✓ كلاس الميتا بوكس موجود</p>";
        } else {
            echo "<p style='color: red;'>✗ كلاس الميتا بوكس غير موجود</p>";
            return;
        }

        // التحقق من الدوال الأساسية
        $methods = array(
            'get_additional_shipping_cost',
            'get_additional_shipping_cost_description'
        );

        foreach ($methods as $method) {
            if (method_exists('Form_Elrakami_Shipping_Cost_Metabox', $method)) {
                echo "<p style='color: green;'>✓ الدالة {$method} موجودة</p>";
            } else {
                echo "<p style='color: red;'>✗ الدالة {$method} غير موجودة</p>";
            }
        }
    }

    /**
     * اختبار رسوم التوصيل للمنتجات البسيطة
     */
    private static function test_simple_product_additional_costs() {
        echo "<h3>اختبار رسوم التوصيل للمنتجات البسيطة</h3>";
        
        // إنشاء منتج تجريبي
        $product = new WC_Product_Simple();
        $product->set_name('منتج تجريبي - رسوم إضافية');
        $product->set_price(100);
        $product->save();
        
        $product_id = $product->get_id();
        
        // إضافة رسوم توصيل إضافية
        $additional_cost = 150;
        $description = 'رسوم تجريبية للاختبار';
        
        update_post_meta($product_id, '_form_elrakami_additional_shipping_cost', $additional_cost);
        update_post_meta($product_id, '_form_elrakami_shipping_cost_description', $description);
        
        // اختبار استرجاع الرسوم
        $retrieved_cost = Form_Elrakami_Shipping_Cost_Metabox::get_additional_shipping_cost($product_id);
        $retrieved_description = Form_Elrakami_Shipping_Cost_Metabox::get_additional_shipping_cost_description($product_id);
        
        if ($retrieved_cost == $additional_cost) {
            echo "<p style='color: green;'>✓ اختبار حفظ واسترجاع الرسوم الإضافية نجح: {$retrieved_cost} دج</p>";
        } else {
            echo "<p style='color: red;'>✗ اختبار حفظ واسترجاع الرسوم فشل: متوقع {$additional_cost}، حصلنا على {$retrieved_cost}</p>";
        }
        
        if ($retrieved_description === $description) {
            echo "<p style='color: green;'>✓ اختبار حفظ واسترجاع الوصف نجح</p>";
        } else {
            echo "<p style='color: red;'>✗ اختبار حفظ واسترجاع الوصف فشل</p>";
        }
        
        // تنظيف
        $product->delete(true);
    }

    /**
     * اختبار رسوم التوصيل للمنتجات المتغيرة
     */
    private static function test_variable_product_additional_costs() {
        echo "<h3>اختبار رسوم التوصيل للمنتجات المتغيرة</h3>";
        
        // إنشاء منتج متغير تجريبي
        $product = new WC_Product_Variable();
        $product->set_name('منتج متغير تجريبي - رسوم إضافية');
        $product->save();
        
        $product_id = $product->get_id();
        
        // إنشاء متغير
        $variation = new WC_Product_Variation();
        $variation->set_parent_id($product_id);
        $variation->set_attributes(array('size' => 'large'));
        $variation->set_price(200);
        $variation->save();
        
        $variation_id = $variation->get_id();
        
        // إضافة رسوم توصيل إضافية للمتغير
        $additional_cost = 250;
        $description = 'رسوم إضافية للحجم الكبير';
        
        update_post_meta($variation_id, '_form_elrakami_additional_shipping_cost', $additional_cost);
        update_post_meta($variation_id, '_form_elrakami_shipping_cost_description', $description);
        
        // اختبار استرجاع الرسوم للمتغير
        $retrieved_cost = Form_Elrakami_Shipping_Cost_Metabox::get_additional_shipping_cost($product_id, $variation_id);
        $retrieved_description = Form_Elrakami_Shipping_Cost_Metabox::get_additional_shipping_cost_description($product_id, $variation_id);
        
        if ($retrieved_cost == $additional_cost) {
            echo "<p style='color: green;'>✓ اختبار رسوم المتغير نجح: {$retrieved_cost} دج</p>";
        } else {
            echo "<p style='color: red;'>✗ اختبار رسوم المتغير فشل: متوقع {$additional_cost}، حصلنا على {$retrieved_cost}</p>";
        }
        
        if ($retrieved_description === $description) {
            echo "<p style='color: green;'>✓ اختبار وصف المتغير نجح</p>";
        } else {
            echo "<p style='color: red;'>✗ اختبار وصف المتغير فشل</p>";
        }
        
        // تنظيف
        $variation->delete(true);
        $product->delete(true);
    }

    /**
     * اختبار حساب التكلفة الإجمالية
     */
    private static function test_cost_calculation() {
        echo "<h3>اختبار حساب التكلفة الإجمالية</h3>";
        
        // إنشاء منتج تجريبي
        $product = new WC_Product_Simple();
        $product->set_name('منتج اختبار التكلفة');
        $product->set_price(100);
        $product->save();
        
        $product_id = $product->get_id();
        
        // إضافة رسوم إضافية
        $additional_cost = 200;
        update_post_meta($product_id, '_form_elrakami_additional_shipping_cost', $additional_cost);
        
        // اختبار التكامل مع نظام WooCommerce
        if (class_exists('Form_Elrakami_Woo_Integration')) {
            $woo_integration = new Form_Elrakami_Woo_Integration();
            
            // استخدام reflection للوصول للدالة الخاصة
            $reflection = new ReflectionClass($woo_integration);
            $method = $reflection->getMethod('calculate_additional_shipping_cost');
            $method->setAccessible(true);
            
            $calculated_cost = $method->invoke($woo_integration, $product);
            
            if ($calculated_cost == $additional_cost) {
                echo "<p style='color: green;'>✓ اختبار حساب التكلفة في نظام WooCommerce نجح: {$calculated_cost} دج</p>";
            } else {
                echo "<p style='color: red;'>✗ اختبار حساب التكلفة فشل: متوقع {$additional_cost}، حصلنا على {$calculated_cost}</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ كلاس التكامل مع WooCommerce غير موجود</p>";
        }
        
        // تنظيف
        $product->delete(true);
    }

    /**
     * اختبار التكامل مع WooCommerce
     */
    private static function test_integration_with_woocommerce() {
        echo "<h3>اختبار التكامل مع WooCommerce</h3>";
        
        // التحقق من وجود WooCommerce
        if (class_exists('WooCommerce')) {
            echo "<p style='color: green;'>✓ WooCommerce متاح</p>";
        } else {
            echo "<p style='color: red;'>✗ WooCommerce غير متاح</p>";
            return;
        }
        
        // التحقق من وجود كلاسات المنتجات
        $product_classes = array(
            'WC_Product_Simple',
            'WC_Product_Variable',
            'WC_Product_Variation'
        );
        
        foreach ($product_classes as $class) {
            if (class_exists($class)) {
                echo "<p style='color: green;'>✓ كلاس {$class} متاح</p>";
            } else {
                echo "<p style='color: red;'>✗ كلاس {$class} غير متاح</p>";
            }
        }
        
        // التحقق من وجود دوال WooCommerce المطلوبة
        $functions = array(
            'wc_get_product',
            'wc_price',
            'update_post_meta',
            'get_post_meta'
        );
        
        foreach ($functions as $function) {
            if (function_exists($function)) {
                echo "<p style='color: green;'>✓ الدالة {$function} متاحة</p>";
            } else {
                echo "<p style='color: red;'>✗ الدالة {$function} غير متاحة</p>";
            }
        }
        
        // اختبار إنشاء منتج بسيط
        try {
            $test_product = new WC_Product_Simple();
            $test_product->set_name('اختبار التكامل');
            $test_product->set_price(50);
            $product_id = $test_product->save();
            
            if ($product_id > 0) {
                echo "<p style='color: green;'>✓ اختبار إنشاء منتج نجح: معرف المنتج {$product_id}</p>";
                
                // حذف المنتج التجريبي
                $test_product->delete(true);
            } else {
                echo "<p style='color: red;'>✗ اختبار إنشاء منتج فشل</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ خطأ في إنشاء منتج: " . $e->getMessage() . "</p>";
        }
    }
}

// تشغيل الاختبارات إذا تم طلبها
if (isset($_GET['run_additional_shipping_costs_tests']) && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-info"><div style="padding: 20px;">';
        Form_Elrakami_Additional_Shipping_Costs_Test::run_tests();
        echo '</div></div>';
    });
}
