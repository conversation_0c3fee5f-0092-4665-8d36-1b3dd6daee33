/**
 * أنماط CSS لإدارة طرق التوصيل
 */

.form-elrakami-shipping-zones {
    max-width: 1200px;
    margin: 20px 0;
}

.form-elrakami-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.form-elrakami-card h2,
.form-elrakami-card h3 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
    color: #23282d;
}

.form-elrakami-card h3 {
    margin-top: 30px;
    font-size: 1.2em;
}

.shipping-companies {
    margin: 20px 0;
}

.company-item {
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 6px;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.company-item:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.company-item.disabled {
    opacity: 0.8;
    background: #f8f9fa;
    border-color: #eee;
}

.company-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.company-header h3 {
    margin: 0;
    padding: 0;
    border: none;
    font-size: 1.1em;
    color: #23282d;
}

.company-actions {
    display: flex;
    gap: 10px;
}

.button-danger {
    color: #fff !important;
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    transition: all 0.2s ease;
}

.button-danger:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
}

.toggle-company,
.delete-company {
    min-width: 80px;
    text-align: center;
    font-weight: 500;
}

.company-status {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
    font-size: 0.9em;
    color: #666;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ccc;
}

.company-item:not(.disabled) .status-indicator {
    background: #46b450;
}

.company-item .notice {
    margin: 10px 0 5px;
}

.company-item .notice.inline {
    margin: 10px 0 0;
    padding: 8px 12px;
    border-radius: 4px;
}

/* نماذج الإضافة والرفع */
#add-company-form {
    margin: 20px 0;
}

#add-company-form input[type="text"] {
    width: 250px;
    margin-left: 10px;
    margin-bottom: 10px;
}

#pricing-upload-form {
    margin: 20px 0;
}

.form-group {
    margin-bottom: 15px;
}

#pricing-upload-form select {
    width: 250px;
    margin-bottom: 5px;
    display: block;
}

#pricing_file {
    display: inline-block;
    margin-left: 10px;
    margin-bottom: 0;
    vertical-align: middle;
}

#pricing-upload-form .button-primary {
    vertical-align: middle;
}

.description {
    color: #666;
    font-style: italic;
    margin: 5px 0 15px;
}

/* تقدم الاستيراد */
#import-progress {
    margin: 15px 0;
    padding: 20px;
    border-radius: 6px;
    display: none;
    font-weight: 500;
    line-height: 1.6;
    border-right: 4px solid #0073aa;
}

#import-progress hr {
    border: none;
    border-top: 1px solid rgba(0,0,0,0.1);
    margin: 15px 0;
}

#import-progress strong {
    display: block;
    margin-top: 15px;
    color: #2271b1;
}

#import-progress.importing {
    text-align: center;
    padding: 20px;
    background: rgba(0,0,0,0.03);
    border-radius: 4px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.import-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
    color: #155724 !important;
    padding: 15px !important;
    border-radius: 4px;
    border-right-color: #46b450 !important;
}

.import-error {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
    color: #721c24 !important;
    padding: 15px !important;
    border-radius: 4px;
    border-right-color: #dc3232 !important;
}

.importing {
    color: #2271b1;
    font-weight: 500;
}

#import-shipping-zones {
    margin-top: 10px;
    font-size: 16px;
    padding: 12px 24px;
}

/* معاينة الملف */
.file-preview-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
    font-size: 14px;
    color: #495057;
}

.file-preview-info strong {
    color: #212529;
    display: block;
    margin-bottom: 8px;
}

/* تحسينات للغة العربية */
body.rtl .form-elrakami-shipping-zones {
    text-align: right;
}

body.rtl .description {
    text-align: right;
}

body.rtl #import-progress {
    border-right: none;
    border-left: 4px solid #0073aa;
}

body.rtl .import-success {
    border-left-color: #46b450 !important;
    border-right: none !important;
}

body.rtl .import-error {
    border-left-color: #dc3232 !important;
    border-right: none !important;
}

body.rtl #add-company-form input[type="text"] {
    margin-left: 0;
    margin-right: 10px;
}

body.rtl .company-item {
    text-align: right;
}

body.rtl #pricing_file {
    margin-left: 0;
    margin-right: 10px;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .company-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .company-actions {
        width: 100%;
        justify-content: flex-end;
    }

    #add-company-form input[type="text"] {
        width: 100%;
        margin: 5px 0;
    }

    #pricing-upload-form select {
        width: 100%;
    }

    .form-group {
        margin-bottom: 10px;
    }
}

/* تحسينات إضافية */
.form-elrakami-card .notice {
    margin: 15px 0;
}

.form-elrakami-card .notice p {
    margin: 0.5em 0;
}

.company-item .notice.notice-success {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.company-item .notice.notice-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* تحسين أزرار الإجراءات */
.company-actions .button {
    font-size: 13px;
    height: auto;
    padding: 6px 12px;
    line-height: 1.4;
    margin-left: 5px;
}

.company-actions .edit-pricing-file {
    background-color: #0073aa;
    color: white;
    border-color: #0073aa;
}

.company-actions .edit-pricing-file:hover {
    background-color: #005a87;
    border-color: #005a87;
}

/* تحسين التباعد */
.form-elrakami-shipping-zones > .notice:first-child {
    margin-top: 0;
}

.form-elrakami-card:first-child {
    margin-top: 0;
}

/* أنماط محرر ملف الأسعار */
.pricing-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pricing-editor-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    cursor: pointer;
}

.pricing-editor-content {
    position: relative;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.pricing-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background: #f8f9fa;
}

.pricing-editor-header h2 {
    margin: 0;
    color: #23282d;
    font-size: 1.3em;
}

.pricing-editor-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.pricing-editor-close:hover {
    background: #e0e0e0;
    color: #333;
}

.pricing-editor-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.editor-info {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
}

.info-item {
    font-size: 14px;
}

.info-item strong {
    color: #2271b1;
}

.editor-help {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
}

.editor-help h4 {
    margin: 0 0 10px 0;
    color: #856404;
}

.editor-help pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    overflow-x: auto;
    margin: 10px 0;
}

.editor-help p {
    margin: 10px 0 0 0;
    font-size: 13px;
    color: #856404;
}

.editor-container {
    margin-bottom: 20px;
}

#pricing-file-content {
    width: 100%;
    min-height: 300px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    background: #fafafa;
    direction: ltr;
    text-align: left;
}

#pricing-file-content:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.editor-validation {
    margin-bottom: 20px;
}

#validation-result {
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
    display: none;
}

#validation-result.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    display: block;
}

#validation-result.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    display: block;
}

.pricing-editor-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.pricing-editor-footer .button {
    min-width: 100px;
}

/* تحسينات للغة العربية */
body.rtl .pricing-editor-content {
    direction: rtl;
}

body.rtl .editor-info {
    text-align: right;
}

body.rtl .editor-help {
    text-align: right;
}

body.rtl .pricing-editor-footer {
    justify-content: flex-start;
}

body.rtl .company-actions .button {
    margin-left: 0;
    margin-right: 5px;
}

/* تحسينات الاستجابة للمحرر */
@media (max-width: 768px) {
    .pricing-editor-content {
        width: 95%;
        max-height: 95vh;
    }

    .pricing-editor-header {
        padding: 15px;
    }

    .pricing-editor-body {
        padding: 15px;
    }

    .editor-info {
        flex-direction: column;
        gap: 10px;
    }

    .pricing-editor-footer {
        padding: 15px;
        flex-wrap: wrap;
    }

    .pricing-editor-footer .button {
        min-width: auto;
        flex: 1;
    }

    #pricing-file-content {
        min-height: 250px;
        font-size: 12px;
    }
}

/* أنماط قسم إعدادات فئات الشحن */
.shipping-class-costs-section {
    margin-top: 30px;
}

.shipping-class-costs-section h2 {
    color: #23282d;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.shipping-class-costs-section p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

#shipping-class-costs-form table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

#shipping-class-costs-form table th {
    background: #f8f9fa;
    color: #23282d;
    font-weight: 600;
    padding: 15px 12px;
    text-align: right;
    border-bottom: 1px solid #ddd;
    font-size: 14px;
}

#shipping-class-costs-form table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

#shipping-class-costs-form table tr:last-child td {
    border-bottom: none;
}

#shipping-class-costs-form table tr:hover {
    background: #f8f9fa;
}

#shipping-class-costs-form table td strong {
    color: #23282d;
    font-weight: 600;
}

#shipping-class-costs-form table td small {
    color: #666;
    font-size: 12px;
    display: block;
    margin-top: 4px;
}

#shipping-class-costs-form input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

#shipping-class-costs-form input[type="number"]:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

#shipping-class-costs-result {
    margin-top: 15px;
}

#shipping-class-costs-result.notice {
    padding: 12px 15px;
    border-radius: 4px;
    border-right: 4px solid;
}

#shipping-class-costs-result.notice-success {
    background: #d4edda;
    border-right-color: #46b450;
    color: #155724;
}

#shipping-class-costs-result.notice-error {
    background: #f8d7da;
    border-right-color: #dc3232;
    color: #721c24;
}

#shipping-class-costs-form .button-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.2s ease;
}

#shipping-class-costs-form .button-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

#shipping-class-costs-form .button-primary:disabled {
    background: #ccc;
    border-color: #ccc;
    cursor: not-allowed;
}

/* تحسينات للغة العربية */
body.rtl #shipping-class-costs-form table th,
body.rtl #shipping-class-costs-form table td {
    text-align: right;
}

body.rtl #shipping-class-costs-result.notice {
    border-right: none;
    border-left: 4px solid;
}

body.rtl #shipping-class-costs-result.notice-success {
    border-left-color: #46b450;
}

body.rtl #shipping-class-costs-result.notice-error {
    border-left-color: #dc3232;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    #shipping-class-costs-form table {
        font-size: 13px;
    }

    #shipping-class-costs-form table th,
    #shipping-class-costs-form table td {
        padding: 10px 8px;
    }

    #shipping-class-costs-form table th {
        font-size: 13px;
    }

    #shipping-class-costs-form input[type="number"] {
        padding: 6px 8px;
        font-size: 13px;
    }
}

/* تحسين عرض الجدول على الشاشات الصغيرة */
@media (max-width: 600px) {
    #shipping-class-costs-form table,
    #shipping-class-costs-form table thead,
    #shipping-class-costs-form table tbody,
    #shipping-class-costs-form table th,
    #shipping-class-costs-form table td,
    #shipping-class-costs-form table tr {
        display: block;
    }

    #shipping-class-costs-form table thead tr {
        position: absolute;
        top: -9999px;
        right: -9999px;
    }

    #shipping-class-costs-form table tr {
        border: 1px solid #ddd;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 4px;
        background: #fff;
    }

    #shipping-class-costs-form table td {
        border: none;
        border-bottom: 1px solid #eee;
        position: relative;
        padding-right: 50%;
        padding-left: 10px;
        padding-top: 10px;
        padding-bottom: 10px;
    }

    #shipping-class-costs-form table td:before {
        content: attr(data-label) ": ";
        position: absolute;
        right: 6px;
        width: 45%;
        padding-left: 10px;
        white-space: nowrap;
        font-weight: bold;
        color: #23282d;
    }

    #shipping-class-costs-form table td:last-child {
        border-bottom: none;
    }
}
