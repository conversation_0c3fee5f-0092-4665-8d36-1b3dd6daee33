/**
 * Fix for JavaScript errors in Form Elrakami admin
 */
jQuery(document).ready(function($) {
    'use strict';

    console.log('Form Elrakami admin fix loaded successfully');

    // Fix for WhatsApp button status
    if ($('#enable-whatsapp-button').length) {
        var whatsappEnabled = $('#enable-whatsapp-button').is(':checked');
        console.log('حالة زر الواتساب عند التحميل:', whatsappEnabled ? 'مفعل' : 'معطل');
    }



    // Fix for quantity element status
    if ($('#quantity-controls-toggle').length) {
        var quantityStatus = $('#quantity-controls-toggle').is(':checked') ? 'show' : 'hide';
        console.log('تم تحديث حالة عنصر الكمية:', quantityStatus);
    }

    // مراقبة تغيير إعداد موضع الكمية
    $('#quantity_position').on('change', function() {
        var selectedPosition = $(this).val();
        console.log('تم تغيير موضع الكمية إلى:', selectedPosition);
    });

    // التحقق من قيمة موضع الكمية عند التحميل
    if ($('#quantity_position').length) {
        var currentPosition = $('#quantity_position').val();
        console.log('موضع الكمية الحالي:', currentPosition);
    }

    // إضافة مراقب لإرسال النموذج
    $('form#form-editor-form').on('submit', function() {
        console.log('تم إرسال النموذج');

        // التحقق من قيمة موضع الكمية قبل الإرسال
        var quantityPosition = $('#quantity_position').val();
        var showQuantityControls = $('#show_quantity_controls').val();

        console.log('إعدادات الكمية قبل الإرسال:', {
            'quantity_position': quantityPosition,
            'show_quantity_controls': showQuantityControls
        });

        // طباعة جميع حقول النموذج
        var formData = $(this).serializeArray();
        console.log('بيانات النموذج:', formData);

        // البحث عن إعدادات الكمية في بيانات النموذج
        var quantitySettings = formData.filter(function(item) {
            return item.name.includes('quantity');
        });
        console.log('إعدادات الكمية في بيانات النموذج:', quantitySettings);
    });
});
