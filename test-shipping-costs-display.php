<?php
/**
 * ملف اختبار سريع لعرض رسوم التوصيل الإضافية
 * 
 * ضع هذا الملف في مجلد الإضافة وافتحه في المتصفح لاختبار الميزة
 */

// تحميل WordPress
require_once('../../../wp-config.php');

// التحقق من الصلاحيات
if (!current_user_can('manage_options')) {
    die('غير مصرح لك بالوصول لهذه الصفحة');
}

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رسوم التوصيل الإضافية - Form Elrakami</title>
    <style>
        body {
            font-family: '<PERSON>goe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f1f1f1;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .success { border-color: #46b450; background: #d4edda; }
        .error { border-color: #dc3232; background: #f8d7da; }
        .warning { border-color: #ffb900; background: #fff3cd; }
        .info { border-color: #0073aa; background: #d1ecf1; }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: right;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .button {
            background: #0073aa;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار رسوم التوصيل الإضافية - Form Elrakami</h1>
        
        <?php
        // اختبار 1: التحقق من وجود الكلاس
        echo '<div class="test-section">';
        echo '<h2>1. اختبار وجود الكلاس</h2>';
        
        if (class_exists('Form_Elrakami_Shipping_Cost_Metabox')) {
            echo '<div class="success">✅ كلاس Form_Elrakami_Shipping_Cost_Metabox موجود</div>';
        } else {
            echo '<div class="error">❌ كلاس Form_Elrakami_Shipping_Cost_Metabox غير موجود</div>';
        }
        echo '</div>';
        
        // اختبار 2: التحقق من وجود WooCommerce
        echo '<div class="test-section">';
        echo '<h2>2. اختبار تكامل WooCommerce</h2>';
        
        if (class_exists('WooCommerce')) {
            echo '<div class="success">✅ WooCommerce مثبت ومفعل</div>';
            
            if (function_exists('wc_get_product')) {
                echo '<div class="success">✅ دوال WooCommerce متاحة</div>';
            } else {
                echo '<div class="error">❌ دوال WooCommerce غير متاحة</div>';
            }
        } else {
            echo '<div class="error">❌ WooCommerce غير مثبت</div>';
        }
        echo '</div>';
        
        // اختبار 3: اختبار المنتجات الموجودة
        echo '<div class="test-section">';
        echo '<h2>3. اختبار المنتجات الموجودة</h2>';
        
        $products = get_posts(array(
            'post_type' => 'product',
            'numberposts' => 10,
            'post_status' => 'publish'
        ));
        
        if (!empty($products)) {
            echo '<div class="info">📦 تم العثور على ' . count($products) . ' منتجات</div>';
            
            echo '<table>';
            echo '<thead><tr><th>اسم المنتج</th><th>النوع</th><th>الرسوم الإضافية</th><th>الوصف</th><th>الإجراءات</th></tr></thead>';
            echo '<tbody>';
            
            foreach ($products as $product_post) {
                $product = wc_get_product($product_post->ID);
                $additional_cost = get_post_meta($product_post->ID, '_form_elrakami_additional_shipping_cost', true);
                $description = get_post_meta($product_post->ID, '_form_elrakami_shipping_cost_description', true);
                
                echo '<tr>';
                echo '<td>' . esc_html($product->get_name()) . '</td>';
                echo '<td>' . esc_html($product->get_type()) . '</td>';
                echo '<td>' . ($additional_cost ? $additional_cost . ' دج' : 'لا يوجد') . '</td>';
                echo '<td>' . ($description ? esc_html($description) : 'لا يوجد') . '</td>';
                echo '<td><a href="' . admin_url('post.php?post=' . $product_post->ID . '&action=edit') . '" class="button">تحرير</a></td>';
                echo '</tr>';
            }
            
            echo '</tbody></table>';
        } else {
            echo '<div class="warning">⚠️ لا توجد منتجات منشورة</div>';
        }
        echo '</div>';
        
        // اختبار 4: اختبار المتغيرات
        echo '<div class="test-section">';
        echo '<h2>4. اختبار المنتجات المتغيرة</h2>';
        
        $variable_products = get_posts(array(
            'post_type' => 'product',
            'numberposts' => 5,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_product_type',
                    'value' => 'variable'
                )
            )
        ));
        
        if (!empty($variable_products)) {
            echo '<div class="info">🔄 تم العثور على ' . count($variable_products) . ' منتجات متغيرة</div>';
            
            foreach ($variable_products as $variable_product_post) {
                $variable_product = wc_get_product($variable_product_post->ID);
                $variations = $variable_product->get_children();
                
                echo '<h4>' . esc_html($variable_product->get_name()) . '</h4>';
                
                if (!empty($variations)) {
                    echo '<table style="margin-bottom: 15px;">';
                    echo '<thead><tr><th>المتغير</th><th>الرسوم الإضافية</th><th>الوصف</th><th>الإجراءات</th></tr></thead>';
                    echo '<tbody>';
                    
                    foreach ($variations as $variation_id) {
                        $variation = wc_get_product($variation_id);
                        $additional_cost = get_post_meta($variation_id, '_form_elrakami_additional_shipping_cost', true);
                        $description = get_post_meta($variation_id, '_form_elrakami_shipping_cost_description', true);
                        
                        echo '<tr>';
                        echo '<td>' . esc_html($variation->get_name()) . '</td>';
                        echo '<td>' . ($additional_cost ? $additional_cost . ' دج' : 'لا يوجد') . '</td>';
                        echo '<td>' . ($description ? esc_html($description) : 'لا يوجد') . '</td>';
                        echo '<td><a href="' . admin_url('post.php?post=' . $variable_product_post->ID . '&action=edit') . '" class="button">تحرير</a></td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody></table>';
                } else {
                    echo '<div class="warning">⚠️ لا توجد متغيرات لهذا المنتج</div>';
                }
            }
        } else {
            echo '<div class="warning">⚠️ لا توجد منتجات متغيرة</div>';
        }
        echo '</div>';
        
        // اختبار 5: اختبار الدوال
        echo '<div class="test-section">';
        echo '<h2>5. اختبار الدوال الأساسية</h2>';
        
        if (class_exists('Form_Elrakami_Shipping_Cost_Metabox')) {
            $methods = array(
                'get_additional_shipping_cost',
                'get_additional_shipping_cost_description'
            );
            
            foreach ($methods as $method) {
                if (method_exists('Form_Elrakami_Shipping_Cost_Metabox', $method)) {
                    echo '<div class="success">✅ الدالة ' . $method . ' موجودة</div>';
                } else {
                    echo '<div class="error">❌ الدالة ' . $method . ' غير موجودة</div>';
                }
            }
        }
        echo '</div>';
        
        // روابط مفيدة
        echo '<div class="test-section info">';
        echo '<h2>🔗 روابط مفيدة</h2>';
        echo '<p>';
        echo '<a href="' . admin_url('edit.php?post_type=product') . '" class="button">إدارة المنتجات</a> ';
        echo '<a href="' . admin_url('post-new.php?post_type=product') . '" class="button">إضافة منتج جديد</a> ';
        echo '<a href="' . admin_url('admin.php?page=form-elrakami-shipping-zones') . '" class="button">إدارة مناطق الشحن</a>';
        echo '</p>';
        echo '</div>';
        ?>
        
        <div class="test-section">
            <h2>📋 تعليمات الاختبار</h2>
            <ol>
                <li><strong>إنشاء منتج جديد:</strong> اذهب إلى المنتجات > إضافة جديد</li>
                <li><strong>البحث عن القسم:</strong> في تبويب "الشحن" ابحث عن "رسوم التوصيل الإضافية - Form Elrakami"</li>
                <li><strong>إضافة رسوم:</strong> أدخل مبلغاً (مثل 200) في حقل "رسوم التوصيل الإضافية"</li>
                <li><strong>إضافة وصف:</strong> أدخل وصفاً مثل "رسوم إضافية للمنتجات الثقيلة"</li>
                <li><strong>حفظ المنتج:</strong> اضغط "تحديث" أو "نشر"</li>
                <li><strong>التحقق:</strong> ارجع لهذه الصفحة وحدثها للتأكد من حفظ البيانات</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🔄 تحديث الصفحة</h2>
            <p>
                <a href="<?php echo $_SERVER['REQUEST_URI']; ?>" class="button">تحديث النتائج</a>
            </p>
        </div>
    </div>
</body>
</html>
