/**
 * أنماط متغيرات المنتج
 */
.form-elrakami-variations {
    margin-bottom: 20px;
    padding: 12px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.variations-title {
    font-size: 14px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 12px;
    color: #1e293b;
}

.variations-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.variation-attribute {
    margin-bottom: 15px;
}

.variation-attribute-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #4b5563;
    font-size: 13px;
}

/* تنسيق حاوية أزرار المتغيرات */
.variation-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

/* إخفاء زر الراديو الأصلي */
.variation-button-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

/* تنسيق تسمية الزر */
.variation-button-label {
    display: inline-block;
    margin: 0;
    cursor: pointer;
}

/* تنسيق زر المتغير */
.variation-button {
    display: inline-block;
    padding: 6px 10px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background-color: #fff;
    color: #4b5563;
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    transition: all 0.2s ease;
}

/* تأثير التحويم على الزر */
.variation-button:hover {
    border-color: #cbd5e1;
    background-color: #f1f5f9;
}

/* تنسيق الزر المحدد */
.variation-button-input:checked + .variation-button {
    border-color: #3b82f6;
    background-color: #eff6ff;
    color: #2563eb;
}

/* تنسيق الزر المعطل */
.variation-button-input:disabled + .variation-button {
    opacity: 0.5;
    cursor: not-allowed;
}

.variation-price-info {
    margin-top: 0px;
    padding-top: 0px;
    border-top: 1px dashed #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.variation-price {
    font-weight: 600;
    color: #2563eb;
    font-size: 14px;
}

.variation-availability {
    font-size: 13px;
}
Botiga
.variation-availability.in-stock {
    color: #10b981;
}

.variation-availability.out-of-stock {
    color: #ef4444;
}

/* تنسيق الزر المعطل */
button.disabled,
button[disabled] {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    background-image: none !important;
    background-color: #cccccc !important;
    border-color: #aaaaaa !important;
}

/* تنسيق الزر المعطل في الشريط المثبت */
.form-elrakami-sticky-bar-button.disabled,
.form-elrakami-sticky-bar-button[disabled] {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    background-image: none !important;
    background-color: #cccccc !important;
    border-color: #aaaaaa !important;
}

/* رسالة عدم التوفر */
.out-of-stock-message {
    color: #ef4444;
    font-weight: bold;
    margin-top: 10px;
    display: block;
    text-align: center;
}

/* تنسيق للهواتف المحمولة */
@media (max-width: 768px) {
    .variations-container {
        grid-template-columns: 1fr;
    }

    .variation-buttons-container {
        justify-content: flex-start;
    }
}
