<?php
/**
 * مدير تحديث فئات الشحن
 * 
 * يدير تحديث الإضافة لدعم فئات الشحن للمستخدمين الحاليين
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * كلاس مدير تحديث فئات الشحن
 */
class Form_Elrakami_Shipping_Classes_Updater {

    /**
     * إصدار قاعدة البيانات الحالي
     */
    const DB_VERSION = '1.1.0';

    /**
     * مفتاح خيار إصدار قاعدة البيانات
     */
    const DB_VERSION_OPTION = 'form_elrakami_db_version';

    /**
     * البناء
     */
    public function __construct() {
        add_action('admin_init', array($this, 'check_for_updates'));
    }

    /**
     * التحقق من وجود تحديثات مطلوبة
     */
    public function check_for_updates() {
        $current_version = get_option(self::DB_VERSION_OPTION, '1.0.0');
        
        if (version_compare($current_version, self::DB_VERSION, '<')) {
            $this->perform_updates($current_version);
        }
    }

    /**
     * تنفيذ التحديثات المطلوبة
     */
    private function perform_updates($current_version) {
        // تحديث إلى الإصدار 1.1.0 - إضافة دعم فئات الشحن
        if (version_compare($current_version, '1.1.0', '<')) {
            $this->update_to_1_1_0();
        }

        // تحديث رقم الإصدار
        update_option(self::DB_VERSION_OPTION, self::DB_VERSION);
    }

    /**
     * تحديث إلى الإصدار 1.1.0
     * إضافة دعم فئات الشحن
     */
    private function update_to_1_1_0() {
        try {
            // إنشاء خيار إعدادات فئات الشحن إذا لم يكن موجوداً
            if (!get_option('form_elrakami_shipping_class_costs')) {
                add_option('form_elrakami_shipping_class_costs', array());
            }

            // تحديث طرق الشحن الموجودة لدعم فئات الشحن
            $this->update_existing_shipping_methods();

            // إضافة إشعار للمدير حول الميزة الجديدة
            $this->add_update_notice();

            error_log('Form Elrakami: تم تحديث الإضافة بنجاح إلى الإصدار 1.1.0 مع دعم فئات الشحن');

        } catch (Exception $e) {
            error_log('Form Elrakami: خطأ في تحديث الإضافة إلى الإصدار 1.1.0 - ' . $e->getMessage());
        }
    }

    /**
     * تحديث طرق الشحن الموجودة لدعم فئات الشحن
     */
    private function update_existing_shipping_methods() {
        if (!class_exists('WC_Shipping_Zones')) {
            return;
        }

        // الحصول على جميع مناطق الشحن
        $zones = WC_Shipping_Zones::get_zones();
        
        // إضافة المنطقة الافتراضية
        $zones[0] = array(
            'zone_id' => 0,
            'zone_name' => 'المنطقة الافتراضية',
            'zone_locations' => array(),
            'shipping_methods' => WC_Shipping_Zones::get_zone(0)->get_shipping_methods()
        );

        foreach ($zones as $zone_data) {
            $zone_id = $zone_data['zone_id'];
            $zone = WC_Shipping_Zones::get_zone($zone_id);
            
            // الحصول على طرق الشحن في هذه المنطقة
            $shipping_methods = $zone->get_shipping_methods();
            
            foreach ($shipping_methods as $instance_id => $shipping_method) {
                // التحقق من أن طريقة الشحن هي flat_rate
                if ($shipping_method->id === 'flat_rate') {
                    $this->update_flat_rate_method($instance_id);
                }
            }
        }
    }

    /**
     * تحديث طريقة شحن flat_rate محددة
     */
    private function update_flat_rate_method($instance_id) {
        // الحصول على إعدادات طريقة الشحن الحالية
        $settings = get_option('woocommerce_flat_rate_' . $instance_id . '_settings', array());
        
        if (empty($settings)) {
            return;
        }

        // التحقق من أن الإعدادات لم يتم تحديثها مسبقاً
        if (isset($settings['type']) && $settings['type'] === 'class') {
            return;
        }

        // إضافة دعم فئات الشحن
        $shipping_classes = WC()->shipping()->get_shipping_classes();
        
        if (!empty($shipping_classes)) {
            foreach ($shipping_classes as $shipping_class) {
                if (!isset($shipping_class->term_id)) {
                    continue;
                }
                
                // إضافة حقل تكلفة الفئة بقيمة افتراضية 0
                $settings['class_cost_' . $shipping_class->term_id] = 0;
            }
            
            // إعداد نوع الحساب
            $settings['type'] = 'class';
            
            // حفظ الإعدادات المحدثة
            update_option('woocommerce_flat_rate_' . $instance_id . '_settings', $settings);
        }
    }

    /**
     * إضافة إشعار للمدير حول الميزة الجديدة
     */
    private function add_update_notice() {
        // إضافة إشعار مؤقت للمدير
        set_transient('form_elrakami_shipping_classes_update_notice', true, WEEK_IN_SECONDS);
        
        // إضافة hook لعرض الإشعار
        add_action('admin_notices', array($this, 'show_update_notice'));
    }

    /**
     * عرض إشعار التحديث
     */
    public function show_update_notice() {
        if (!get_transient('form_elrakami_shipping_classes_update_notice')) {
            return;
        }

        if (!current_user_can('manage_options')) {
            return;
        }

        ?>
        <div class="notice notice-success is-dismissible" id="form-elrakami-update-notice">
            <h3>🎉 ميزة جديدة في Form Elrakami!</h3>
            <p><strong>تم إضافة دعم فئات الشحن (Shipping Classes)</strong></p>
            <p>يمكنك الآن تحديد تكاليف شحن مختلفة للمنتجات حسب فئاتها (مثل المنتجات الثقيلة أو الهشة).</p>
            <ul style="list-style: disc; margin-right: 20px;">
                <li>اذهب إلى <strong>Form Elrakami > إدارة مناطق الشحن</strong></li>
                <li>انتقل إلى قسم <strong>"إعدادات فئات الشحن"</strong></li>
                <li>حدد التكاليف الإضافية لكل فئة</li>
                <li>طبق الفئات على منتجاتك من صفحة تحرير المنتج</li>
            </ul>
            <p>
                <a href="<?php echo admin_url('admin.php?page=form-elrakami-shipping-zones'); ?>" class="button button-primary">
                    إعداد فئات الشحن الآن
                </a>
                <button type="button" class="notice-dismiss" onclick="dismissUpdateNotice()">
                    <span class="screen-reader-text">إخفاء هذا الإشعار</span>
                </button>
            </p>
        </div>
        
        <script>
        function dismissUpdateNotice() {
            jQuery.post(ajaxurl, {
                action: 'form_elrakami_dismiss_update_notice',
                _ajax_nonce: '<?php echo wp_create_nonce('form_elrakami_dismiss_notice'); ?>'
            });
            jQuery('#form-elrakami-update-notice').fadeOut();
        }
        </script>
        <?php
    }

    /**
     * معالج AJAX لإخفاء إشعار التحديث
     */
    public static function dismiss_update_notice() {
        check_ajax_referer('form_elrakami_dismiss_notice', '_ajax_nonce');
        
        if (current_user_can('manage_options')) {
            delete_transient('form_elrakami_shipping_classes_update_notice');
            wp_send_json_success();
        } else {
            wp_send_json_error('غير مصرح');
        }
    }
}

// تهيئة مدير التحديث
new Form_Elrakami_Shipping_Classes_Updater();

// تسجيل معالج AJAX
add_action('wp_ajax_form_elrakami_dismiss_update_notice', array('Form_Elrakami_Shipping_Classes_Updater', 'dismiss_update_notice'));
