<?php
/**
 * Customers list template
 */

// Get blocked customers from database
global $wpdb;
$blocked_table = $wpdb->prefix . 'form_elrakami_blocked_customers';

// Handle pagination
$per_page = 20;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $per_page;

// Get filter values
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : 'all';
$search_filter = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
$customer_type = isset($_GET['customer_type']) ? sanitize_text_field($_GET['customer_type']) : 'all';

// Get all WooCommerce customers with orders
$customers = array();
$blocked_ids = array(); // لتخزين معرفات العملاء المحظورين

// الحصول على قائمة العملاء المحظورين
$blocked_query = "SELECT * FROM {$blocked_table} WHERE status = 'active'";
$blocked_list = $wpdb->get_results($blocked_query);

// إنشاء قائمة بأرقام هواتف وعناوين IP للعملاء المحظورين
foreach ($blocked_list as $blocked) {
    if (!empty($blocked->phone_number)) {
        $blocked_ids['phone'][$blocked->phone_number] = $blocked;
    }
    if (!empty($blocked->ip_address)) {
        $blocked_ids['ip'][$blocked->ip_address] = $blocked;
    }
    if (!empty($blocked->email)) {
        $blocked_ids['email'][$blocked->email] = $blocked;
    }
}

// الحصول على طلبات WooCommerce
$args = array(
    'limit' => -1,
    'return' => 'ids',
);

// إضافة البحث إذا كان موجودًا
if (!empty($search_filter)) {
    $args['customer'] = $search_filter;
}

$order_ids = wc_get_orders($args);

// مصفوفة لتخزين العملاء الفريدين
$unique_customers = array();
$customer_keys = array(); // لتتبع العملاء الفريدين عن طريق الهاتف أو IP

// الحصول على بيانات العملاء من جميع الطلبات أولاً
foreach ($order_ids as $order_id) {
    $order = wc_get_order($order_id);
    if (!$order) continue;

    $customer_phone = $order->get_billing_phone();
    $customer_email = $order->get_billing_email();
    $customer_name = $order->get_formatted_billing_full_name();
    $customer_ip = $order->get_customer_ip_address();
    $order_date = $order->get_date_created()->date_i18n(get_option('date_format') . ' ' . get_option('time_format'));
    $order_total = $order->get_total();
    $order_status = $order->get_status();

    // إنشاء مفتاح فريد للعميل (الأولوية لرقم الهاتف ثم البريد الإلكتروني ثم IP)
    $customer_key = !empty($customer_phone) ? 'phone_' . $customer_phone :
                   (!empty($customer_email) ? 'email_' . $customer_email :
                   (!empty($customer_ip) ? 'ip_' . $customer_ip : 'order_' . $order_id));

    // التحقق مما إذا كان العميل محظورًا
    $is_blocked = false;
    $block_reason = '';
    $block_id = 0;

    if (!empty($customer_phone) && isset($blocked_ids['phone'][$customer_phone])) {
        $is_blocked = true;
        $block_reason = $blocked_ids['phone'][$customer_phone]->reason;
        $block_id = $blocked_ids['phone'][$customer_phone]->id;
    } elseif (!empty($customer_ip) && isset($blocked_ids['ip'][$customer_ip])) {
        $is_blocked = true;
        $block_reason = $blocked_ids['ip'][$customer_ip]->reason;
        $block_id = $blocked_ids['ip'][$customer_ip]->id;
    } elseif (!empty($customer_email) && isset($blocked_ids['email'][$customer_email])) {
        $is_blocked = true;
        $block_reason = $blocked_ids['email'][$customer_email]->reason;
        $block_id = $blocked_ids['email'][$customer_email]->id;
    }

    // تصفية حسب نوع العميل (محظور/غير محظور)
    if ($customer_type === 'blocked' && !$is_blocked) {
        continue;
    } elseif ($customer_type === 'unblocked' && $is_blocked) {
        continue;
    }

    // إذا لم يكن العميل موجودًا بالفعل، أضفه إلى القائمة
    if (!isset($customer_keys[$customer_key])) {
        $customer_keys[$customer_key] = true;

        $unique_customers[] = array(
            'order_id' => $order_id,
            'name' => $customer_name,
            'phone' => $customer_phone,
            'email' => $customer_email,
            'ip' => $customer_ip,
            'date' => $order_date,
            'total' => $order_total,
            'status' => $order_status,
            'is_blocked' => $is_blocked,
            'block_reason' => $block_reason,
            'block_id' => $block_id,
            'orders_count' => 1 // عدد الطلبات لهذا العميل
        );
    } else {
        // إذا كان العميل موجودًا بالفعل، قم بتحديث عدد الطلبات فقط
        foreach ($unique_customers as $key => $customer) {
            $existing_key = !empty($customer['phone']) ? 'phone_' . $customer['phone'] :
                           (!empty($customer['email']) ? 'email_' . $customer['email'] :
                           (!empty($customer['ip']) ? 'ip_' . $customer['ip'] : 'order_' . $customer['order_id']));

            if ($existing_key === $customer_key) {
                $unique_customers[$key]['orders_count']++;
                // تحديث معلومات الطلب إلى أحدث طلب إذا كان هذا الطلب أحدث
                if (strtotime($order_date) > strtotime($unique_customers[$key]['date'])) {
                    $unique_customers[$key]['date'] = $order_date;
                    $unique_customers[$key]['total'] = $order_total;
                    $unique_customers[$key]['status'] = $order_status;
                    $unique_customers[$key]['order_id'] = $order_id;
                }
                break;
            }
        }
    }
}

// ترتيب العملاء حسب تاريخ آخر طلب (الأحدث أولاً)
usort($unique_customers, function($a, $b) {
    return strtotime($b['date']) - strtotime($a['date']);
});

$total_items = count($unique_customers);

// تقسيم العملاء حسب الصفحة الحالية
$customers = array_slice($unique_customers, $offset, $per_page);

// Calculate pagination
$total_pages = ceil($total_items / $per_page);

// Handle block/unblock action
if (isset($_GET['action']) && !empty($_GET['id'])) {
    $customer_id = intval($_GET['id']);
    $action = sanitize_text_field($_GET['action']);

    if (($action === 'block' || $action === 'unblock') && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], $action . '_customer_' . $customer_id)) {
        $new_status = ($action === 'block') ? 'active' : 'inactive';

        $wpdb->update(
            $blocked_table,
            array('status' => $new_status),
            array('id' => $customer_id),
            array('%s'),
            array('%d')
        );

        // Redirect to avoid resubmission
        wp_redirect(add_query_arg(array('message' => ($action === 'block') ? 1 : 2), remove_query_arg(array('action', 'id', '_wpnonce'))));
        exit;
    }
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && !empty($_GET['id'])) {
    $customer_id = intval($_GET['id']);

    if (isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'delete_customer_' . $customer_id)) {
        $wpdb->delete(
            $blocked_table,
            array('id' => $customer_id),
            array('%d')
        );

        // Redirect to avoid resubmission
        wp_redirect(add_query_arg(array('message' => 3), remove_query_arg(array('action', 'id', '_wpnonce'))));
        exit;
    }
}

// Handle add/edit customer form submission
if (isset($_POST['form_elrakami_customer_nonce']) && wp_verify_nonce($_POST['form_elrakami_customer_nonce'], 'save_blocked_customer')) {
    $customer_id = isset($_POST['customer_id']) ? intval($_POST['customer_id']) : 0;
    $customer_name = isset($_POST['customer_name']) ? sanitize_text_field($_POST['customer_name']) : '';
    $phone_number = isset($_POST['phone_number']) ? sanitize_text_field($_POST['phone_number']) : '';
    $ip_address = isset($_POST['ip_address']) ? sanitize_text_field($_POST['ip_address']) : '';
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    $reason = isset($_POST['reason']) ? sanitize_textarea_field($_POST['reason']) : '';
    $notes = isset($_POST['notes']) ? sanitize_textarea_field($_POST['notes']) : '';

    // Validate data - at least one identifier must be provided
    if (empty($phone_number) && empty($ip_address) && empty($email)) {
        add_settings_error(
            'form_elrakami_customers',
            'missing_identifier',
            'يجب توفير رقم هاتف أو عنوان IP أو بريد إلكتروني على الأقل.',
            'error'
        );
    } else {
        $data = array(
            'customer_name' => $customer_name,
            'phone_number' => $phone_number,
            'ip_address' => $ip_address,
            'email' => $email,
            'reason' => $reason,
            'notes' => $notes,
            'status' => 'active',
            'blocked_by' => get_current_user_id()
        );

        $format = array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d');

        if ($customer_id > 0) {
            // Update existing customer
            $wpdb->update(
                $blocked_table,
                $data,
                array('id' => $customer_id),
                $format,
                array('%d')
            );
            $message = 'تم تحديث العميل المحظور بنجاح.';
        } else {
            // Add blocked_at timestamp for new entries
            $data['blocked_at'] = current_time('mysql');
            $format[] = '%s';

            // Insert new customer
            $wpdb->insert(
                $blocked_table,
                $data,
                $format
            );
            $message = 'تم إضافة العميل إلى قائمة الحظر بنجاح.';
        }

        add_settings_error(
            'form_elrakami_customers',
            'customer_saved',
            $message,
            'updated'
        );

        // Refresh the page to show updated data
        wp_redirect(add_query_arg(array('message' => ($customer_id > 0) ? 4 : 5), remove_query_arg(array('edit', 'id'))));
        exit;
    }
}

// Display messages if any
if (isset($_GET['message']) && !isset($_GET['edit'])) {
    $message_type = intval($_GET['message']);

    $messages = array(
        1 => 'تم حظر العميل بنجاح.',
        2 => 'تم إلغاء حظر العميل بنجاح.',
        3 => 'تم حذف العميل من قائمة الحظر بنجاح.',
        4 => 'تم تحديث بيانات العميل بنجاح.',
        5 => 'تم إضافة العميل إلى قائمة الحظر بنجاح.'
    );

    if (isset($messages[$message_type])) {
        echo '<div class="notice notice-success is-dismissible"><p>' . $messages[$message_type] . '</p></div>';
    }
}

// Display settings errors if any
settings_errors('form_elrakami_customers');

// Get customer data for edit mode
$edit_mode = isset($_GET['edit']) && !empty($_GET['id']);
$customer_data = array();

if ($edit_mode) {
    $customer_id = intval($_GET['id']);
    $customer_data = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$blocked_table} WHERE id = %d", $customer_id), ARRAY_A);

    if (!$customer_data) {
        echo '<div class="notice notice-error is-dismissible"><p>العميل غير موجود.</p></div>';
        $edit_mode = false;
    }
}
?>

<div class="wrap">
    <?php if ($edit_mode) : ?>
        <h1 class="wp-heading-inline">تعديل بيانات العميل المحظور</h1>
        <a href="<?php echo admin_url('admin.php?page=form-elrakami-customers'); ?>" class="page-title-action">العودة إلى القائمة</a>
    <?php else : ?>
        <h1 class="wp-heading-inline">قائمة العملاء</h1>
        <button type="button" id="add-blocked-customer-btn" class="page-title-action">إضافة عميل محظور</button>
    <?php endif; ?>

    <hr class="wp-header-end">

    <!-- شريط الشعار -->
    <?php include(plugin_dir_path(dirname(__FILE__)) . 'partials/header-bar.php'); ?>

    <?php if ($edit_mode) : ?>
        <!-- Edit/Add Customer Form -->
        <div class="card">
            <h2><?php echo $customer_data ? 'تعديل بيانات العميل' : 'إضافة عميل محظور جديد'; ?></h2>

            <form method="post" action="">
                <?php wp_nonce_field('save_blocked_customer', 'form_elrakami_customer_nonce'); ?>
                <input type="hidden" name="customer_id" value="<?php echo $customer_data ? esc_attr($customer_data['id']) : 0; ?>">

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="customer_name">اسم العميل</label></th>
                        <td>
                            <input type="text" name="customer_name" id="customer_name" class="regular-text"
                                   value="<?php echo $customer_data ? esc_attr($customer_data['customer_name']) : ''; ?>">
                            <p class="description">اسم العميل (اختياري)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="phone_number">رقم الهاتف</label></th>
                        <td>
                            <input type="text" name="phone_number" id="phone_number" class="regular-text"
                                   value="<?php echo $customer_data ? esc_attr($customer_data['phone_number']) : ''; ?>">
                            <p class="description">رقم هاتف العميل (مطلوب واحد على الأقل من: رقم الهاتف، عنوان IP، البريد الإلكتروني)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="ip_address">عنوان IP</label></th>
                        <td>
                            <input type="text" name="ip_address" id="ip_address" class="regular-text"
                                   value="<?php echo $customer_data ? esc_attr($customer_data['ip_address']) : ''; ?>">
                            <p class="description">عنوان IP الخاص بالعميل (مطلوب واحد على الأقل من: رقم الهاتف، عنوان IP، البريد الإلكتروني)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="email">البريد الإلكتروني</label></th>
                        <td>
                            <input type="email" name="email" id="email" class="regular-text"
                                   value="<?php echo $customer_data ? esc_attr($customer_data['email']) : ''; ?>">
                            <p class="description">البريد الإلكتروني للعميل (مطلوب واحد على الأقل من: رقم الهاتف، عنوان IP، البريد الإلكتروني)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="reason">سبب الحظر</label></th>
                        <td>
                            <textarea name="reason" id="reason" rows="3" class="large-text"><?php echo $customer_data ? esc_textarea($customer_data['reason']) : ''; ?></textarea>
                            <p class="description">سبب حظر هذا العميل</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="notes">ملاحظات إضافية</label></th>
                        <td>
                            <textarea name="notes" id="notes" rows="3" class="large-text"><?php echo $customer_data ? esc_textarea($customer_data['notes']) : ''; ?></textarea>
                            <p class="description">ملاحظات إضافية حول هذا العميل (اختياري)</p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php echo $customer_data ? 'تحديث العميل' : 'إضافة العميل'; ?>">
                    <a href="<?php echo admin_url('admin.php?page=form-elrakami-customers'); ?>" class="button">إلغاء</a>
                </p>
            </form>
        </div>
    <?php else : ?>
        <!-- Filters -->
        <form method="get" class="customers-filter">
            <input type="hidden" name="page" value="form-elrakami-customers">

            <div class="tablenav top">
                <div class="alignleft actions">
                    <label for="customer-type-filter" class="screen-reader-text">تصفية حسب نوع العميل</label>
                    <select name="customer_type" id="customer-type-filter">
                        <option value="all" <?php selected($customer_type, 'all'); ?>>جميع العملاء</option>
                        <option value="blocked" <?php selected($customer_type, 'blocked'); ?>>العملاء المحظورين</option>
                        <option value="unblocked" <?php selected($customer_type, 'unblocked'); ?>>العملاء غير المحظورين</option>
                    </select>

                    <label for="search-filter" class="screen-reader-text">بحث</label>
                    <input type="search" id="search-filter" name="s" value="<?php echo esc_attr($search_filter); ?>" placeholder="بحث بالاسم، رقم الهاتف، البريد الإلكتروني...">

                    <input type="submit" class="button" value="تصفية">
                    <?php if ($customer_type !== 'all' || !empty($search_filter)) : ?>
                        <a href="<?php echo admin_url('admin.php?page=form-elrakami-customers'); ?>" class="button">إعادة ضبط</a>
                    <?php endif; ?>
                </div>

                <?php if ($total_pages > 1) : ?>
                    <div class="tablenav-pages">
                        <span class="displaying-num"><?php printf(_n('%s عنصر', '%s عناصر', $total_items, 'form-elrakami'), number_format_i18n($total_items)); ?></span>
                        <span class="pagination-links">
                            <?php
                            $big = 999999999;
                            echo paginate_links(array(
                                'base' => str_replace($big, '%#%', esc_url(add_query_arg('paged', $big, remove_query_arg('message')))),
                                'format' => '&paged=%#%',
                                'current' => $current_page,
                                'total' => $total_pages,
                                'prev_text' => '&laquo;',
                                'next_text' => '&raquo;',
                            ));
                            ?>
                        </span>
                    </div>
                <?php endif; ?>

                <br class="clear">
            </div>
        </form>

        <?php if (empty($customers)) : ?>
            <div class="notice notice-info">
                <p>لا يوجد عملاء متطابقين مع معايير البحث.</p>
            </div>
        <?php else : ?>
            <table class="wp-list-table widefat fixed striped customers-table">
                <thead>
                    <tr>
                        <th scope="col" class="manage-column column-id">آخر طلب</th>
                        <th scope="col" class="manage-column column-name">اسم العميل</th>
                        <th scope="col" class="manage-column column-phone">رقم الهاتف</th>
                        <th scope="col" class="manage-column column-email">البريد الإلكتروني</th>
                        <th scope="col" class="manage-column column-ip">عنوان IP</th>
                        <th scope="col" class="manage-column column-date">تاريخ آخر طلب</th>
                        <th scope="col" class="manage-column column-orders">عدد الطلبات</th>
                        <th scope="col" class="manage-column column-total">آخر مبلغ</th>
                        <th scope="col" class="manage-column column-status">الحالة</th>
                        <th scope="col" class="manage-column column-actions">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($customers as $customer) : ?>
                        <tr<?php echo $customer['is_blocked'] ? ' class="blocked-row"' : ''; ?>>
                            <td>
                                <a href="<?php echo admin_url('post.php?post=' . $customer['order_id'] . '&action=edit'); ?>" target="_blank">
                                    #<?php echo $customer['order_id']; ?>
                                </a>
                            </td>
                            <td><?php echo esc_html($customer['name']); ?></td>
                            <td><?php echo esc_html($customer['phone']); ?></td>
                            <td><?php echo esc_html($customer['email']); ?></td>
                            <td><?php echo esc_html($customer['ip']); ?></td>
                            <td><?php echo $customer['date']; ?></td>
                            <td>
                                <span class="orders-count"><?php echo $customer['orders_count']; ?></span>
                            </td>
                            <td><?php echo wc_price($customer['total']); ?></td>
                            <td>
                                <?php if ($customer['is_blocked']) : ?>
                                    <span class="status-active" title="<?php echo esc_attr($customer['block_reason']); ?>">محظور</span>
                                <?php else : ?>
                                    <span class="status-inactive">نشط</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="row-actions">
                                    <?php if ($customer['is_blocked']) : ?>
                                        <span class="unblock">
                                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=form-elrakami-customers&action=unblock&id=' . $customer['block_id']), 'unblock_customer_' . $customer['block_id']); ?>" class="unblock-customer">إلغاء الحظر</a>
                                        </span>
                                    <?php else : ?>
                                        <span class="block">
                                            <a href="#" class="block-customer-btn"
                                               data-name="<?php echo esc_attr($customer['name']); ?>"
                                               data-phone="<?php echo esc_attr($customer['phone']); ?>"
                                               data-email="<?php echo esc_attr($customer['email']); ?>"
                                               data-ip="<?php echo esc_attr($customer['ip']); ?>">
                                                حظر
                                            </a>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th scope="col" class="manage-column column-id">آخر طلب</th>
                        <th scope="col" class="manage-column column-name">اسم العميل</th>
                        <th scope="col" class="manage-column column-phone">رقم الهاتف</th>
                        <th scope="col" class="manage-column column-email">البريد الإلكتروني</th>
                        <th scope="col" class="manage-column column-ip">عنوان IP</th>
                        <th scope="col" class="manage-column column-date">تاريخ آخر طلب</th>
                        <th scope="col" class="manage-column column-orders">عدد الطلبات</th>
                        <th scope="col" class="manage-column column-total">آخر مبلغ</th>
                        <th scope="col" class="manage-column column-status">الحالة</th>
                        <th scope="col" class="manage-column column-actions">إجراءات</th>
                    </tr>
                </tfoot>
            </table>

            <?php if ($total_pages > 1) : ?>
                <div class="tablenav bottom">
                    <div class="tablenav-pages">
                        <span class="displaying-num"><?php printf(_n('%s عنصر', '%s عناصر', $total_items, 'form-elrakami'), number_format_i18n($total_items)); ?></span>
                        <span class="pagination-links">
                            <?php
                            $big = 999999999;
                            echo paginate_links(array(
                                'base' => str_replace($big, '%#%', esc_url(add_query_arg('paged', $big, remove_query_arg('message')))),
                                'format' => '&paged=%#%',
                                'current' => $current_page,
                                'total' => $total_pages,
                                'prev_text' => '&laquo;',
                                'next_text' => '&raquo;',
                            ));
                            ?>
                        </span>
                    </div>
                    <br class="clear">
                </div>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.customers-table .column-id {
    width: 5%;
}
.customers-table .column-name {
    width: 15%;
}
.customers-table .column-phone {
    width: 10%;
}
.customers-table .column-email {
    width: 15%;
}
.customers-table .column-ip {
    width: 10%;
}
.customers-table .column-date {
    width: 10%;
}
.customers-table .column-orders {
    width: 5%;
}
.customers-table .column-total {
    width: 7%;
}
.customers-table .column-status {
    width: 7%;
}
.customers-table .column-actions {
    width: 8%;
}

.customers-table a {
    text-decoration: none;
}

.customers-table .block-customer-btn {
    color: #d32f2f;
    font-weight: bold;
}

.orders-count {
    display: inline-block;
    background-color: #2271b1;
    color: white;
    font-weight: bold;
    padding: 2px 8px;
    border-radius: 10px;
    text-align: center;
    min-width: 20px;
}
.customers-filter {
    margin-bottom: 20px;
}
.status-active {
    color: #d63638;
    font-weight: bold;
}
.status-inactive {
    color: #2271b1;
}

/* تنسيق النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    padding: 20px;
    border: 1px solid #ddd;
    width: 400px;
    max-width: 90%;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

.modal-close {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 20px;
    cursor: pointer;
}

.modal-title {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.modal-form-row {
    margin-bottom: 15px;
}

.modal-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.modal-form-row input,
.modal-form-row select,
.modal-form-row textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.modal-actions {
    text-align: left;
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.block-options {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.block-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.block-option input[type="checkbox"] {
    width: auto;
    margin-right: 5px;
}

.block-option label {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    font-weight: normal;
    width: 100%;
}

.block-option span {
    font-weight: bold;
    margin-right: 5px;
    color: #333;
}

.blocked-row {
    background-color: #ffebee !important;
}

.status-active {
    color: #d32f2f;
    font-weight: bold;
    background-color: #ffebee;
    padding: 3px 8px;
    border-radius: 4px;
    display: inline-block;
}

.status-inactive {
    color: #2e7d32;
    font-weight: bold;
    background-color: #e8f5e9;
    padding: 3px 8px;
    border-radius: 4px;
    display: inline-block;
}
</style>

<!-- نافذة منبثقة لإضافة عميل محظور بسرعة -->
<div id="quick-block-modal" class="modal">
    <div class="modal-content">
        <span class="modal-close">&times;</span>
        <h3 class="modal-title">إضافة عميل محظور</h3>

        <form id="quick-block-form" method="post">
            <?php wp_nonce_field('save_blocked_customer', 'form_elrakami_customer_nonce'); ?>
            <input type="hidden" id="customer-name" name="customer_name" value="">

            <div class="modal-form-row">
                <label for="block-type">نوع الحظر</label>
                <select id="block-type" name="block-type">
                    <option value="phone">رقم الهاتف</option>
                    <option value="ip">عنوان IP</option>
                    <option value="email">البريد الإلكتروني</option>
                </select>
            </div>

            <div class="modal-form-row">
                <label for="block-value">قيمة الحظر</label>
                <input type="text" id="block-value" name="block-value" placeholder="أدخل رقم الهاتف أو عنوان IP أو البريد الإلكتروني" required>
            </div>

            <div class="modal-form-row">
                <label for="block-reason">سبب الحظر</label>
                <textarea id="block-reason" name="block-reason" rows="3" placeholder="أدخل سبب الحظر (اختياري)"></textarea>
            </div>

            <div class="modal-actions">
                <button type="button" class="button modal-cancel">إلغاء</button>
                <button type="submit" class="button button-primary">حظر العميل</button>
            </div>
        </form>
    </div>
</div>

<!-- نافذة منبثقة لحظر عميل محدد -->
<div id="block-specific-customer-modal" class="modal">
    <div class="modal-content">
        <span class="modal-close">&times;</span>
        <h3 class="modal-title">حظر العميل: <span id="customer-name-display"></span></h3>

        <form id="block-specific-customer-form" method="post">
            <?php wp_nonce_field('save_blocked_customer', 'form_elrakami_customer_nonce'); ?>
            <input type="hidden" id="specific-customer-name" name="customer_name" value="">

            <div class="modal-form-row">
                <label>اختر طريقة الحظر:</label>
                <div class="block-options">
                    <div class="block-option">
                        <input type="checkbox" id="block-phone" name="block_phone" value="1">
                        <label for="block-phone">حظر رقم الهاتف: <span id="phone-display"></span></label>
                        <input type="hidden" id="phone-value" name="phone_number" value="">
                    </div>

                    <div class="block-option">
                        <input type="checkbox" id="block-email" name="block_email" value="1">
                        <label for="block-email">حظر البريد الإلكتروني: <span id="email-display"></span></label>
                        <input type="hidden" id="email-value" name="email" value="">
                    </div>

                    <div class="block-option">
                        <input type="checkbox" id="block-ip" name="block_ip" value="1">
                        <label for="block-ip">حظر عنوان IP: <span id="ip-display"></span></label>
                        <input type="hidden" id="ip-value" name="ip_address" value="">
                    </div>
                </div>
            </div>

            <div class="modal-form-row">
                <label for="specific-block-reason">سبب الحظر</label>
                <textarea id="specific-block-reason" name="reason" rows="3" placeholder="أدخل سبب الحظر" required></textarea>
            </div>

            <div class="modal-actions">
                <button type="button" class="button modal-cancel">إلغاء</button>
                <button type="submit" class="button button-primary">حظر العميل</button>
            </div>
        </form>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Confirm unblock action
    $('.unblock-customer').on('click', function(e) {
        return confirm('هل أنت متأكد من إلغاء حظر هذا العميل؟');
    });

    // إضافة عميل محظور بسرعة
    $('#add-blocked-customer-btn').on('click', function() {
        $('#quick-block-modal').fadeIn(200);
    });

    // فتح نافذة حظر عميل محدد
    $('.block-customer-btn').on('click', function(e) {
        e.preventDefault();

        var name = $(this).data('name');
        var phone = $(this).data('phone');
        var email = $(this).data('email');
        var ip = $(this).data('ip');

        // تعيين قيم العميل في النموذج
        $('#customer-name-display').text(name);
        $('#specific-customer-name').val(name);

        // تعيين قيم الحظر
        if (phone) {
            $('#phone-display').text(phone);
            $('#phone-value').val(phone);
            $('#block-phone').prop('checked', true);
        } else {
            $('#block-phone').prop('checked', false).prop('disabled', true);
            $('#phone-display').text('(غير متوفر)');
        }

        if (email) {
            $('#email-display').text(email);
            $('#email-value').val(email);
            $('#block-email').prop('checked', true);
        } else {
            $('#block-email').prop('checked', false).prop('disabled', true);
            $('#email-display').text('(غير متوفر)');
        }

        if (ip) {
            $('#ip-display').text(ip);
            $('#ip-value').val(ip);
            $('#block-ip').prop('checked', true);
        } else {
            $('#block-ip').prop('checked', false).prop('disabled', true);
            $('#ip-display').text('(غير متوفر)');
        }

        // فتح النافذة المنبثقة
        $('#block-specific-customer-modal').fadeIn(200);
    });

    // إغلاق النوافذ المنبثقة
    $('.modal-close, .modal-cancel').on('click', function() {
        $('.modal').fadeOut(200);
    });

    // تقديم نموذج الحظر السريع
    $('#quick-block-form').on('submit', function(e) {
        e.preventDefault();

        var blockValue = $('#block-value').val();
        var blockType = $('#block-type').val();
        var blockReason = $('#block-reason').val();

        if (!blockValue) {
            alert('يرجى إدخال قيمة للحظر');
            return;
        }

        // تعيين القيمة في الحقل المناسب حسب نوع الحظر
        var formData = {
            'form_elrakami_customer_nonce': $('#form_elrakami_customer_nonce').val(),
            'customer_id': 0,
            'reason': blockReason
        };

        if (blockType === 'phone') {
            formData.phone_number = blockValue;
        } else if (blockType === 'ip') {
            formData.ip_address = blockValue;
        } else if (blockType === 'email') {
            formData.email = blockValue;
        }

        // إرسال النموذج
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'form_elrakami_block_customer',
                nonce: $('#form_elrakami_customer_nonce').val(),
                data: formData
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || 'حدث خطأ أثناء حظر العميل');
                }
            },
            error: function() {
                alert('حدث خطأ في الاتصال بالخادم');
            }
        });
    });

    // تقديم نموذج حظر عميل محدد
    $('#block-specific-customer-form').on('submit', function(e) {
        e.preventDefault();

        // التحقق من اختيار طريقة حظر واحدة على الأقل
        if (!$('#block-phone').is(':checked') && !$('#block-email').is(':checked') && !$('#block-ip').is(':checked')) {
            alert('يرجى اختيار طريقة حظر واحدة على الأقل');
            return;
        }

        // التحقق من إدخال سبب الحظر
        var reason = $('#specific-block-reason').val().trim();
        if (!reason) {
            alert('يرجى إدخال سبب الحظر');
            return;
        }

        // تجميع البيانات
        var formData = {
            'form_elrakami_customer_nonce': $('#form_elrakami_customer_nonce').val(),
            'customer_id': 0,
            'customer_name': $('#specific-customer-name').val(),
            'reason': reason
        };

        if ($('#block-phone').is(':checked')) {
            formData.phone_number = $('#phone-value').val();
        }

        if ($('#block-email').is(':checked')) {
            formData.email = $('#email-value').val();
        }

        if ($('#block-ip').is(':checked')) {
            formData.ip_address = $('#ip-value').val();
        }

        // إرسال النموذج
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'form_elrakami_block_customer',
                nonce: $('#form_elrakami_customer_nonce').val(),
                data: formData
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || 'حدث خطأ أثناء حظر العميل');
                }
            },
            error: function() {
                alert('حدث خطأ في الاتصال بالخادم');
            }
        });
    });

    // تمييز الصفوف المحظورة
    $('.blocked-row').css('background-color', '#ffebee');
});
</script>
