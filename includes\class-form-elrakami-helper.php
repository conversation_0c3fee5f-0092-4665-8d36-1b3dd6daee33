<?php
/**
 * Helper class with utility functions.
 *
 * @link       https://elrakami.com
 * @since      1.0.0
 *
 * @package    Form_Elrakami
 */

/**
 * Helper class with utility functions.
 *
 * This class provides common utility functions used across the plugin.
 *
 * @since      1.0.0
 * @package    Form_Elrakami
 */
class Form_Elrakami_Helper {

    /**
     * Free database results to prevent "Commands out of sync" errors.
     *
     * This helps prevent the "Commands out of sync; you can't run this command now" error
     * that can occur when multiple database operations are performed without freeing results.
     *
     * @since    1.0.0
     * @static
     */
    public static function free_results() {
        global $wpdb;

        // Check if we have a valid database connection
        if (!$wpdb || !is_object($wpdb)) {
            return;
        }

        // Check database handle
        if (!isset($wpdb->dbh)) {
            return;
        }

        // First try using the wpdb flush method if available
        if (method_exists($wpdb, 'flush')) {
            $wpdb->flush();
            return;
        }

        // For mysqli connections, handle multiple result sets
        if ($wpdb->use_mysqli && is_object($wpdb->dbh)) {
            $connection = $wpdb->dbh;

            try {
                while (mysqli_more_results($connection)) {
                    mysqli_next_result($connection);
                    if ($result = mysqli_store_result($connection)) {
                        mysqli_free_result($result);
                    }
                }
            } catch (Exception $e) {
                // Silent exception handling - don't break the site if this fails
                error_log('Form Elrakami: Error freeing MySQL results: ' . $e->getMessage());
            }
        }

        // No need for legacy mysql functions as they are deprecated in modern PHP
    }

    /**
     * Get municipality name from code.
     *
     * @since    1.0.0
     * @static
     * @param    string    $municipality_code    Municipality code.
     * @param    string    $state                State code.
     * @return   string                         Municipality name.
     */
    public static function get_municipality_name($municipality_code, $state = '') {
        // تسجيل معلومات الاستدعاء للتشخيص
        error_log('استدعاء get_municipality_name مع القيم - البلدية: ' . $municipality_code . ', الولاية: ' . $state);

        // If function exists in global scope, use it
        if (function_exists('get_municipality_name')) {
            $result = get_municipality_name($municipality_code, $state);
            error_log('نتيجة من get_municipality_name العالمية: ' . $result);
            return $result;
        }

        // Fallback implementation
        // إذا كان الرمز فارغًا، أرجع قيمة فارغة
        if (empty($municipality_code)) {
            error_log('رمز البلدية فارغ، إرجاع قيمة فارغة');
            return '';
        }

        // If we have access to the get_municipalities_for_state function
        if (function_exists('get_municipalities_for_state')) {
            // تحضير رمز الولاية - إذا كان بدون بادئة DZ، أزل البادئة
            if (strpos($state, 'DZ-') === 0) {
                $state = substr($state, 3);
                error_log('تم تعديل رمز الولاية إلى: ' . $state);
            }

            // إذا كان رمز الولاية غير فارغ، نبحث في بلديات تلك الولاية فقط
            if (!empty($state)) {
                $municipalities = get_municipalities_for_state($state);
                error_log('عدد البلديات المسترجعة للولاية ' . $state . ': ' . count($municipalities));

                foreach ($municipalities as $municipality_name) {
                    $sanitized_name = sanitize_title($municipality_name);
                    if ($sanitized_name === $municipality_code) {
                        error_log('تم العثور على مطابقة للبلدية: ' . $municipality_name);
                        return $municipality_name;
                    }
                }

                // محاولة ثانية مع إجراء مقارنة أكثر مرونة
                foreach ($municipalities as $municipality_name) {
                    if (strtolower($municipality_name) === strtolower($municipality_code) ||
                        strpos(strtolower($municipality_name), strtolower($municipality_code)) !== false ||
                        strpos(strtolower($municipality_code), strtolower($municipality_name)) !== false) {
                        error_log('تم العثور على مطابقة مرنة للبلدية: ' . $municipality_name);
                        return $municipality_name;
                    }
                }
            } else {
                // إذا لم نعرف الولاية، نبحث في جميع الولايات (بطيء ولكن شامل)
                error_log('لم يتم تحديد الولاية، سيتم البحث في جميع الولايات');
                $all_states = array(
                    '01', '02', '03', '04', '05', '06', '07', '08', '09', '10',
                    '11', '12', '13', '14', '15', '16', '17', '18', '19', '20',
                    '21', '22', '23', '24', '25', '26', '27', '28', '29', '30',
                    '31', '32', '33', '34', '35', '36', '37', '38', '39', '40',
                    '41', '42', '43', '44', '45', '46', '47', '48', '49', '50',
                    '51', '52', '53', '54', '55', '56', '57', '58'
                );

                foreach ($all_states as $current_state) {
                    $municipalities = get_municipalities_for_state($current_state);

                    foreach ($municipalities as $municipality_name) {
                        if (sanitize_title($municipality_name) === $municipality_code) {
                            error_log('تم العثور على مطابقة للبلدية في الولاية ' . $current_state . ': ' . $municipality_name);
                            return $municipality_name;
                        }
                    }
                }
            }
        } else {
            error_log('وظيفة get_municipalities_for_state غير موجودة');
        }

        // في حالة عدم العثور على اسم البلدية، نقوم بمعالجة خاصة للمفاتيح
        if (empty($municipality_code) || $municipality_code === '-') {
            error_log('رمز البلدية فارغ أو "-"، محاولة استخدام معلومات البلدية المباشرة');

            // التحقق من وجود بيانات في $_POST
            if (isset($_POST['municipality_text']) && !empty($_POST['municipality_text'])) {
                $municipality_text = sanitize_text_field($_POST['municipality_text']);
                error_log('تم استخدام municipality_text: ' . $municipality_text);
                return $municipality_text;
            }
        }

        // في حالة عدم العثور على اسم البلدية، نرجع الرمز نفسه مع رسالة تشخيصية
        error_log('لم يتم العثور على مطابقة للبلدية، إرجاع الرمز الأصلي: ' . $municipality_code);
        return $municipality_code;
    }

    /**
     * Safely serialize data for storage.
     *
     * @since    1.0.0
     * @static
     * @param    mixed    $data    Data to serialize.
     * @return   string            Serialized data.
     */
    public static function serialize_data($data) {
        // Simple JSON encoding is safer than PHP serialize
        return wp_json_encode($data);
    }

    /**
     * Safely unserialize data from storage.
     *
     * @since    1.0.0
     * @static
     * @param    string    $data    Serialized data.
     * @return   mixed              Unserialized data.
     */
    public static function unserialize_data($data) {
        // First try JSON decode (safer approach)
        $decoded = json_decode($data, true);

        // If JSON decode worked and returned an array or object
        if ($decoded !== null && (is_array($decoded) || is_object($decoded))) {
            return $decoded;
        }

        // Fallback to PHP unserialize for backward compatibility
        // with appropriate security measures
        if (is_string($data) && trim($data) !== '') {
            // Use PHP unserialize with options to prevent code execution
            // Available in PHP 7.0+
            if (version_compare(PHP_VERSION, '7.0.0', '>=')) {
                return @unserialize($data, ['allowed_classes' => false]);
            } else {
                // For PHP < 7.0, use regular unserialize but with safeguards
                // Do a simple pattern check for potentially malicious serialized objects
                if (preg_match('/[oc]:[+\-]?\d+:/', $data)) {
                    return [];
                }
                return @unserialize($data);
            }
        }

        // If all else fails, return an empty array
        return [];
    }

    /**
     * Get human-readable label for field type.
     *
     * @since    1.0.0
     * @static
     * @param    string    $type    Field type.
     * @return   string             Human-readable label.
     */
    public static function get_field_type_label($type) {
        $labels = array(
            'text' => 'نص',
            'textarea' => 'نص متعدد الأسطر',
            'number' => 'رقم',
            'email' => 'بريد إلكتروني',
            'tel' => 'هاتف',
            'select' => 'قائمة منسدلة',
            'radio' => 'اختيار فردي',
            'checkbox' => 'اختيار متعدد',
            'date' => 'تاريخ',
            'time' => 'وقت',
            'url' => 'رابط',
            'file' => 'ملف',
            'password' => 'كلمة مرور',
            'hidden' => 'حقل مخفي',
            'country' => 'دولة',
            'state' => 'ولاية',
            'municipality' => 'بلدية',
            'product_quantity' => 'كمية المنتج',
            'submit' => 'زر الإرسال',
            'name' => 'اسم',
            'address' => 'عنوان',
            'separator' => 'فاصل',
            'html' => 'HTML',
            'heading' => 'عنوان',
            'paragraph' => 'فقرة',
            'price' => 'سعر',
            'shipping' => 'شحن',
            'coupon' => 'كوبون',
            'payment' => 'دفع',
            // يمكن إضافة المزيد من الأنواع هنا
        );

        return isset($labels[$type]) ? $labels[$type] : $type;
    }
}