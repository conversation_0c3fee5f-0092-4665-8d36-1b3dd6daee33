.form-elrakami-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
    flex-direction: row-reverse; /* عكس الترتيب لجعل الكمية على اليمين */
}

/* إخفاء عنصر الكمية عند تعطيله */
.form-elrakami-form.hide-quantity .quantity-controls {
    display: none !important;
}

/* تعديل عرض زر الطلب عند إخفاء عنصر الكمية */
.form-elrakami-form.hide-quantity .form-elrakami-actions {
    justify-content: center;
}

.form-elrakami-form.hide-quantity .form-elrakami-submit {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}

/* إخفاء عنصر الكمية عند تعطيله */
.form-elrakami-form[data-show-quantity="hide"] .quantity-controls {
    display: none;
}

/* تعديل عرض زر الطلب عند إخفاء عنصر الكمية */
.form-elrakami-form[data-show-quantity="hide"] .form-elrakami-submit {
    width: 100%;
    justify-content: center;
}

.form-elrakami-actions.no-quantity .quantity-controls {
    display: none;
}

/* تنسيقات موضع الكمية بجانب زر الطلب */
.form-elrakami-actions.inline-quantity {
    flex-direction: row-reverse; /* الحفاظ على عكس الترتيب */
    justify-content: center;
    gap: 8px;
    align-items: center;
}

.form-elrakami-actions.inline-quantity .quantity-controls {
    order: 2;
    margin-left: 8px;
    margin-right: 0;
    width: auto;
    flex: 0 0 auto;
}

.form-elrakami-actions.inline-quantity .form-elrakami-submit {
    order: 1;
    flex: 1;
    max-width: 70%;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid var(--form-input-border);
    border-radius: var(--form-input-radius);
    overflow: hidden;
    height: 50px;
    min-height: 50px;  /* مطابقة لارتفاع زر الطلب الجديد */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

@media (max-width: 480px) {
    .form-elrakami-actions {
        flex-direction: column-reverse;
        align-items: stretch;
    }

    .form-elrakami-submit {
        width: 100%;
    }

    .quantity-controls {
        width: 100%;
        justify-content: center;
    }
}

.quantity-btn {
    background: var(--form-input-bg);
    border: none;
    padding: 0 15px;
    cursor: pointer;
    color: var(--form-text-color);
    font-size: 16px;
    font-weight: 600;
    transition: all 0.2s ease;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 45px;
}

.quantity-btn:hover {
    background-color: var(--form-primary-color);
    color: white;
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: none;
    border-left: 1px solid var(--form-input-border);
    border-right: 1px solid var(--form-input-border);
    padding: 0;
    height: 100%;
    background: white;
    -moz-appearance: textfield;
    font-size: 16px;
    font-weight: 600;
    color: var(--form-text-color);
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/* تنسيقات قسم تفاصيل الأسعار */
.total-price-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.price-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #e2e8f0;
}

.price-row:last-child {
    border-bottom: none;
}

.price-row.total {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 2px solid #e2e8f0;
    font-weight: bold;
    font-size: 1.1em;
    color: #2563eb;
}

.price-label {
    color: #4b5563;
    font-weight: 500;
}

.product-price-display,
.shipping-price-display,
.total-price-display {
    font-weight: 600;
    color: #1a5b8e;
}

/* تنسيق بطاقة التوصيل المجاني */
.free-shipping-badge {
    background-color: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
    display: inline-block;
    font-size: 12px;
}

/* تنسيق رسالة اختيار الولاية */
.select-state-message {
    color: #f59e0b;
    font-weight: 500;
    font-size: 12px;
}

/* تنسيق بطاقة العبارة بجانب السعر */
.price-note-badge {
    background-color: #4f46e5;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
    display: inline-block;
    font-size: 12px;
    margin-right: 8px;
    vertical-align: middle;
}

/* تنسيق عرض الكمية بجانب سعر المنتج */
.product-quantity-display {
    display: inline-block;
    font-weight: 500;
    color: #64748b;
    margin-right: 5px;
}

/* تنسيق حاوية سعر المنتج */
.product-price-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

/* تنسيقات رسائل النموذج */
.form-message {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 6px;
    font-weight: 500;
    text-align: center;
}

.form-message.success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.form-message.error {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
    font-size: 14px;
}

/* تنسيقات رسائل الخطأ للحقول */
.field-error {
    color: #b91c1c;
    font-size: 12px;
    margin-top: 5px;
    font-weight: 500;
}

/* تنسيقات رسالة الحظر */
.form-message.blocked-message {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    font-size: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}