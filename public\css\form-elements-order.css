/**
 * تنسيقات CSS لدعم ترتيب عناصر النموذج
 * هذا الملف يحتوي على تنسيقات CSS لدعم ترتيب العناصر المختلفة في النموذج
 */

/* تنسيقات حاوية عنصر اختيار الكمية */
.quantity-controls-container {
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

.quantity-controls-container .quantity-controls {
    display: flex;
    align-items: center;
    background-color: var(--form-input-bg, #f8fafc);
    border: 1px solid var(--form-input-border, #e2e8f0);
    border-radius: var(--form-input-radius, 6px);
    overflow: hidden;
    max-width: 150px;
}

/* تنسيقات ملخص الطلب */
.total-price-container {
    margin: 20px 0;
    padding: 15px;
    background-color: rgba(var(--form-primary-rgb, 37, 99, 235), 0.05);
    border-radius: var(--form-radius, 8px);
    border: 1px solid rgba(var(--form-primary-rgb, 37, 99, 235), 0.1);
}

/* تنسيقات منطقة طرق التوصيل */
.form-elrakami-address-fields {
    margin: 20px 0;
}

/* تنسيقات منطقة زر الطلب */
.form-elrakami-actions {
    margin: 20px 0;
    display: flex;
    flex-direction: row-reverse; /* عكس الترتيب لجعل الكمية على اليمين */
    align-items: center;
    gap: 15px;
    justify-content: space-between;
}

/* تنسيقات للتباعد بين العناصر */
.form-elrakami-form > div {
    margin-bottom: 20px;
}

/* تنسيقات للتباعد بين العناصر في حالة الترتيب المخصص */
.form-elrakami-form > div:last-child {
    margin-bottom: 0;
}

/* تنسيقات للتباعد بين العناصر في حالة الترتيب المخصص */
.form-elrakami-form > div:first-child {
    margin-top: 20px;
}

/* تنسيقات للتباعد بين العناصر في حالة الترتيب المخصص */
.form-elrakami-form h2 + div {
    margin-top: 20px;
}

/* تنسيقات للتباعد بين العناصر في حالة الترتيب المخصص */
.form-elrakami-form .form-elrakami-description + div {
    margin-top: 20px;
}
