<?php
/**
 * Fired during plugin deactivation
 *
 * @link       https://elrakami.com
 * @since      1.0.0
 *
 * @package    Form_Elrakami
 */

/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since      1.0.0
 * @package    Form_Elrakami
 */
class Form_Elrakami_Deactivator {

    /**
     * Perform cleanup on plugin deactivation.
     *
     * @since    1.0.0
     */
    public static function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();

        // تنظيف مناطق الشحن الجزائرية لتجنب الأخطاء
        self::cleanup_shipping_zones();

        // Note: We don't delete plugin data on deactivation
        // This allows users to reactivate the plugin without losing their forms and data
        // Data is only removed if the user chooses to delete the plugin
    }

    /**
     * تنظيف مناطق الشحن الجزائرية وإزالة المراجع المعطلة
     *
     * @since    1.0.3
     */
    public static function cleanup_shipping_zones() {
        if (!class_exists('WC_Shipping_Zones')) {
            return;
        }

        global $wpdb;

        try {
            // إزالة جميع مناطق الشحن الجزائرية
            $zones = WC_Shipping_Zones::get_zones();
            foreach ($zones as $zone_data) {
                $zone = new WC_Shipping_Zone($zone_data['id']);
                $locations = $zone->get_zone_locations();

                // التحقق من وجود مواقع جزائرية
                foreach ($locations as $location) {
                    if ($location->type === 'state' && strpos($location->code, 'DZ:DZ-') === 0) {
                        $zone->delete();
                        break;
                    }
                }
            }

            // تنظيف قاعدة البيانات من المراجع المعطلة
            self::cleanup_orphaned_shipping_data();

            // إعادة تعيين إعدادات WooCommerce للشحن
            delete_option('woocommerce_allowed_countries');
            delete_option('woocommerce_specific_allowed_countries');

        } catch (Exception $e) {
            error_log('Form Elrakami: خطأ في تنظيف مناطق الشحن - ' . $e->getMessage());
        }
    }

    /**
     * تنظيف البيانات المعطلة من جداول الشحن
     *
     * @since    1.0.3
     */
    private static function cleanup_orphaned_shipping_data() {
        global $wpdb;

        // حذف المواقع المعطلة
        $wpdb->query("
            DELETE FROM {$wpdb->prefix}woocommerce_shipping_zone_locations
            WHERE location_code LIKE 'DZ:DZ-%'
            AND zone_id NOT IN (
                SELECT zone_id FROM {$wpdb->prefix}woocommerce_shipping_zones
            )
        ");

        // حذف طرق الشحن المعطلة
        $wpdb->query("
            DELETE FROM {$wpdb->prefix}woocommerce_shipping_zone_methods
            WHERE zone_id NOT IN (
                SELECT zone_id FROM {$wpdb->prefix}woocommerce_shipping_zones
            )
        ");

        // تنظيف خيارات طرق الشحن المعطلة
        $orphaned_options = $wpdb->get_results("
            SELECT option_name FROM {$wpdb->prefix}options
            WHERE option_name LIKE 'woocommerce_flat_rate_%_settings'
            AND SUBSTRING(option_name, 21, LOCATE('_settings', option_name) - 21) NOT IN (
                SELECT instance_id FROM {$wpdb->prefix}woocommerce_shipping_zone_methods
            )
        ");

        foreach ($orphaned_options as $option) {
            delete_option($option->option_name);
        }
    }

}