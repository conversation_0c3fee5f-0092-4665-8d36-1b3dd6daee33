<?php
/**
 * Define the internationalization functionality.
 *
 * Loads and defines the internationalization files for this plugin
 * so that it is ready for translation.
 *
 * @since      1.0.0
 * @package    Form_Elrakami
 */

class Form_Elrakami_i18n {

    /**
     * The domain specified for this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $domain    The domain identifier for this plugin.
     */
    private $domain;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $domain    The domain identifier for this plugin.
     */
    public function __construct($domain = 'form-elrakami') {
        $this->domain = $domain;
    }

    /**
     * Load the plugin text domain for translation.
     *
     * @since    1.0.0
     */
    public function load_plugin_textdomain() {
        // تحديد مسار مجلد اللغات
        $languages_path = dirname(dirname(plugin_basename(__FILE__))) . '/languages/';

        // تحميل ملف الترجمة الأساسي
        load_plugin_textdomain(
            $this->domain,
            false,
            $languages_path
        );

        // تحميل ملفات الترجمة الإضافية حسب اللغة الحالية
        $current_language = self::get_current_language();

        if ($current_language !== 'ar') {
            $locale_map = array(
                'fr' => 'fr_FR',
                'en' => 'en_US',
                'es' => 'es_ES'
            );

            if (isset($locale_map[$current_language])) {
                $locale = $locale_map[$current_language];
                $mo_file = WP_PLUGIN_DIR . '/' . dirname(dirname(plugin_basename(__FILE__))) . '/languages/form-elrakami-' . $locale . '.mo';

                if (file_exists($mo_file)) {
                    load_textdomain($this->domain, $mo_file);
                }
            }
        }
    }

    /**
     * Get the current site language.
     *
     * @since    1.0.0
     * @return   string    The current language code.
     */
    public static function get_current_language() {
        // Check if a specific language is set in the plugin settings
        $plugin_language = get_option('form_elrakami_language', 'auto');

        // If language is set to a specific value in settings, use it
        if ($plugin_language !== 'auto') {
            return $plugin_language;
        }

        // Otherwise, detect the site language
        $locale = get_locale();

        // If WPML is active, use its language
        if (defined('ICL_LANGUAGE_CODE')) {
            $locale = ICL_LANGUAGE_CODE;
        }

        // If Polylang is active, use its language
        if (function_exists('pll_current_language')) {
            $pll_locale = pll_current_language('locale');
            if (!empty($pll_locale)) {
                $locale = $pll_locale;
            }
        }

        // Return the first two characters of the locale (language code)
        return substr($locale, 0, 2);
    }

    /**
     * Check if the current language is French.
     *
     * @since    1.0.0
     * @return   boolean   True if the current language is French.
     */
    public static function is_french() {
        $lang = self::get_current_language();
        return $lang === 'fr';
    }

    /**
     * Check if the current language is English.
     *
     * @since    1.0.0
     * @return   boolean   True if the current language is English.
     */
    public static function is_english() {
        $lang = self::get_current_language();
        return $lang === 'en';
    }

    /**
     * Check if the current language is LTR (Left-to-Right).
     *
     * @since    1.0.0
     * @return   boolean   True if the current language is LTR.
     */
    public static function is_ltr() {
        $lang = self::get_current_language();
        return in_array($lang, array('fr', 'en', 'es'));
    }

    /**
     * Get available languages for the plugin.
     *
     * @since    1.0.0
     * @return   array     Array of available languages.
     */
    public static function get_available_languages() {
        return array(
            'auto' => __('تلقائي (حسب لغة الموقع)', 'form-elrakami'),
            'ar' => __('العربية', 'form-elrakami'),
            'fr' => __('الفرنسية', 'form-elrakami'),
            'en' => __('الإنجليزية', 'form-elrakami'),
            'es' => __('الإسبانية', 'form-elrakami')
        );
    }

    /**
     * Get the current language name.
     *
     * @since    1.0.0
     * @return   string    The current language name.
     */
    public static function get_current_language_name() {
        $languages = self::get_available_languages();
        $current = self::get_current_language();

        if ($current === 'auto') {
            return $languages['auto'];
        }

        return isset($languages[$current]) ? $languages[$current] : $languages['ar'];
    }

    /**
     * Translate a string based on the current language.
     *
     * @since    1.0.0
     * @param    string    $arabic     The Arabic text.
     * @param    string    $french     The French text.
     * @param    string    $english    The English text.
     * @param    string    $spanish    The Spanish text.
     * @return   string                The translated text.
     */
    public static function translate($arabic, $french = '', $english = '', $spanish = '') {
        $lang = self::get_current_language();

        if ($lang === 'fr' && !empty($french)) {
            return $french;
        } elseif ($lang === 'en' && !empty($english)) {
            return $english;
        } elseif ($lang === 'es' && !empty($spanish)) {
            return $spanish;
        }

        return $arabic;
    }
}
