<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إعدادات ملخص الطلب</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #2271b1;
            border-bottom: 2px solid #2271b1;
            padding-bottom: 10px;
        }
        
        /* تطبيق أنماط ملخص الطلب */
        .total-price-container {
            border: 1px solid var(--summary-border-color, #e2e8f0);
            border-radius: var(--summary-border-radius, 12px);
            background-color: var(--summary-bg-color, rgba(37, 99, 235, 0.05));
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 14px;
            background-color: var(--summary-header-bg-color, #f1f5f9);
            border-bottom: 1px solid var(--summary-border-color, #e2e8f0);
            cursor: pointer;
        }

        .summary-title {
            margin: 0;
            font-size: 15px;
            font-weight: 600;
            color: var(--summary-title-color, #1e293b);
            display: flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }

        .summary-title i {
            color: var(--summary-price-color, #3730a3);
            font-size: 14px;
        }

        .summary-toggle-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .price-details {
            padding: 12px 14px;
        }

        .price-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .price-row:last-child {
            margin-bottom: 0;
        }

        .price-row.total {
            border-top: 1px solid #e2e8f0;
            padding-top: 8px;
            margin-top: 8px;
            font-weight: 600;
        }

        .price-label {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #64748b;
        }

        .price-label i {
            color: var(--summary-price-color, #3730a3);
            font-size: 12px;
        }

        .product-price-display,
        .shipping-price-display,
        .total-price-display {
            font-weight: 600;
            color: var(--summary-price-color, #3730a3);
            background-color: var(--summary-price-bg-color, rgba(55, 48, 163, 0.05));
            padding: 3px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 13px;
        }

        .total-price-display {
            font-size: 14px;
            background-color: var(--summary-price-bg-color, rgba(55, 48, 163, 0.08));
            padding: 4px 8px;
        }

        .product-quantity-badge {
            background-color: var(--summary-price-bg-color, rgba(55, 48, 163, 0.1));
            color: var(--summary-price-color, #3730a3);
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
            margin-right: 6px;
        }

        /* أنماط الزر مع السعر */
        .order-button {
            background-color: #3730a3;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 20px 0;
        }

        .button-total-price {
            display: inline-flex;
            align-items: center;
            margin-right: 8px;
            font-weight: 600;
            opacity: 0.9;
        }

        .button-total-price .total-price-display {
            background-color: rgba(255, 255, 255, 0.2);
            color: inherit;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 600;
        }

        /* أنماط مخصصة للاختبار */
        .custom-colors-1 {
            --summary-bg-color: rgba(220, 38, 127, 0.05);
            --summary-border-color: rgba(220, 38, 127, 0.2);
            --summary-header-bg-color: #fce7f3;
            --summary-title-color: #be185d;
            --summary-price-color: #be185d;
            --summary-price-bg-color: rgba(220, 38, 127, 0.1);
            --summary-border-radius: 20px;
        }

        .custom-colors-2 {
            --summary-bg-color: rgba(34, 197, 94, 0.05);
            --summary-border-color: rgba(34, 197, 94, 0.2);
            --summary-header-bg-color: #f0fdf4;
            --summary-title-color: #15803d;
            --summary-price-color: #15803d;
            --summary-price-bg-color: rgba(34, 197, 94, 0.1);
            --summary-border-radius: 8px;
        }

        .controls {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }

        .controls button {
            margin: 5px;
            padding: 8px 15px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }

        .controls button:hover {
            background-color: #f0f0f0;
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار إعدادات ملخص الطلب - Form Elrakami</h1>
        
        <div class="test-section">
            <h2>1. ملخص الطلب الافتراضي</h2>
            <div class="total-price-container">
                <div class="summary-header">
                    <h3 class="summary-title">
                        <i class="fas fa-file-invoice"></i> ملخص الطلب
                    </h3>
                    <button type="button" class="summary-toggle-btn">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
                <div class="price-details">
                    <div class="price-row">
                        <span class="price-label">
                            <i class="fas fa-tag"></i> سعر المنتج: 
                            <span class="product-quantity-badge">x<span class="quantity-number">1</span></span>
                        </span>
                        <span class="product-price-display">150.00 دج</span>
                    </div>
                    <div class="price-row">
                        <span class="price-label">
                            <i class="fas fa-truck"></i> رسوم التوصيل:
                        </span>
                        <span class="shipping-price-display">50.00 دج</span>
                    </div>
                    <div class="price-row total">
                        <span class="price-label">
                            <i class="fas fa-calculator"></i> المجموع الإجمالي:
                        </span>
                        <span class="total-price-display">200.00 دج</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>2. ملخص الطلب بألوان مخصصة (وردي)</h2>
            <div class="total-price-container custom-colors-1">
                <div class="summary-header">
                    <h3 class="summary-title">
                        <i class="fas fa-file-invoice"></i> ملخص الطلب
                    </h3>
                    <button type="button" class="summary-toggle-btn">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
                <div class="price-details">
                    <div class="price-row">
                        <span class="price-label">
                            <i class="fas fa-tag"></i> سعر المنتج: 
                            <span class="product-quantity-badge">x<span class="quantity-number">2</span></span>
                        </span>
                        <span class="product-price-display">300.00 دج</span>
                    </div>
                    <div class="price-row">
                        <span class="price-label">
                            <i class="fas fa-truck"></i> رسوم التوصيل:
                        </span>
                        <span class="shipping-price-display">75.00 دج</span>
                    </div>
                    <div class="price-row total">
                        <span class="price-label">
                            <i class="fas fa-calculator"></i> المجموع الإجمالي:
                        </span>
                        <span class="total-price-display">375.00 دج</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>3. ملخص الطلب بألوان مخصصة (أخضر)</h2>
            <div class="total-price-container custom-colors-2">
                <div class="summary-header">
                    <h3 class="summary-title">
                        <i class="fas fa-file-invoice"></i> ملخص الطلب
                    </h3>
                    <button type="button" class="summary-toggle-btn">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
                <div class="price-details">
                    <div class="price-row">
                        <span class="price-label">
                            <i class="fas fa-tag"></i> سعر المنتج: 
                            <span class="product-quantity-badge">x<span class="quantity-number">1</span></span>
                        </span>
                        <span class="product-price-display">250.00 دج</span>
                    </div>
                    <div class="price-row">
                        <span class="price-label">
                            <i class="fas fa-truck"></i> رسوم التوصيل:
                        </span>
                        <span class="shipping-price-display">مجاني</span>
                    </div>
                    <div class="price-row total">
                        <span class="price-label">
                            <i class="fas fa-calculator"></i> المجموع الإجمالي:
                        </span>
                        <span class="total-price-display">250.00 دج</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>4. زر الطلب مع السعر (عند إخفاء الملخص)</h2>
            <p>هذا مثال على كيفية ظهور زر الطلب عندما يكون ملخص الطلب مخفياً:</p>
            <button class="order-button">
                إتمام الطلب
                <span class="button-total-price">
                    (<span class="total-price-display">200.00 دج</span>)
                </span>
                <i class="fas fa-check"></i>
            </button>
        </div>

        <div class="controls">
            <h3>عناصر التحكم للاختبار:</h3>
            <button onclick="toggleSummary(1)">تبديل الملخص الأول</button>
            <button onclick="toggleSummary(2)">تبديل الملخص الثاني</button>
            <button onclick="toggleSummary(3)">تبديل الملخص الثالث</button>
            <button onclick="hideAllSummaries()">إخفاء جميع الملخصات</button>
            <button onclick="showAllSummaries()">إظهار جميع الملخصات</button>
        </div>
    </div>

    <script>
        // تفعيل أزرار التبديل
        document.querySelectorAll('.summary-toggle-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const details = this.closest('.total-price-container').querySelector('.price-details');
                const icon = this.querySelector('i');
                
                if (details.style.display === 'none') {
                    details.style.display = 'block';
                    icon.className = 'fas fa-chevron-up';
                } else {
                    details.style.display = 'none';
                    icon.className = 'fas fa-chevron-down';
                }
            });
        });

        function toggleSummary(index) {
            const summaries = document.querySelectorAll('.total-price-container');
            const summary = summaries[index - 1];
            const btn = summary.querySelector('.summary-toggle-btn');
            btn.click();
        }

        function hideAllSummaries() {
            document.querySelectorAll('.total-price-container').forEach(container => {
                container.classList.add('hidden');
            });
        }

        function showAllSummaries() {
            document.querySelectorAll('.total-price-container').forEach(container => {
                container.classList.remove('hidden');
            });
        }
    </script>
</body>
</html>
