/**
 * معالج الأمان المحسن لإضافة Form Elrakami
 * 
 * يتعامل مع:
 * - تجديد رموز الأمان (nonce) المنتهية الصلاحية
 * - معالجة أخطاء الخادم
 * - إعادة المحاولة التلقائية
 * - رسائل خطأ واضحة للمستخدم
 * 
 * @since 1.0.3
 */

(function($) {
    'use strict';

    // متغيرات عامة
    var securityHandler = {
        maxRetries: 3,
        retryDelay: 1000,
        nonceRefreshInProgress: false,
        
        /**
         * تهيئة معالج الأمان
         */
        init: function() {
            this.setupGlobalAjaxHandler();
            this.setupNonceRefresh();
        },

        /**
         * إعداد معالج AJAX العام
         */
        setupGlobalAjaxHandler: function() {
            var self = this;
            
            // معالج عام لجميع طلبات AJAX
            $(document).ajaxError(function(event, xhr, settings, thrownError) {
                // التحقق من أخطاء الأمان
                if (self.isSecurityError(xhr)) {
                    self.handleSecurityError(xhr, settings);
                }
                // التحقق من أخطاء الخادم
                else if (self.isServerError(xhr)) {
                    self.handleServerError(xhr, settings);
                }
            });
        },

        /**
         * التحقق من وجود خطأ أمني
         */
        isSecurityError: function(xhr) {
            if (xhr.status === 403) return true;
            
            try {
                var response = JSON.parse(xhr.responseText);
                if (response && response.data) {
                    var errorMessage = response.data.toLowerCase();
                    return errorMessage.includes('nonce') || 
                           errorMessage.includes('security') || 
                           errorMessage.includes('token') ||
                           errorMessage.includes('أمان') ||
                           errorMessage.includes('رمز');
                }
            } catch (e) {
                // تجاهل أخطاء تحليل JSON
            }
            
            return false;
        },

        /**
         * التحقق من وجود خطأ في الخادم
         */
        isServerError: function(xhr) {
            return xhr.status >= 500 || xhr.status === 0;
        },

        /**
         * معالجة أخطاء الأمان
         */
        handleSecurityError: function(xhr, settings) {
            var self = this;
            
            console.log('تم اكتشاف خطأ أمني، محاولة تجديد رمز الأمان...');
            
            // تجديد رمز الأمان وإعادة المحاولة
            this.refreshNonce(function(success) {
                if (success) {
                    console.log('تم تجديد رمز الأمان بنجاح، إعادة المحاولة...');
                    self.retryAjaxRequest(settings);
                } else {
                    console.error('فشل في تجديد رمز الأمان');
                    self.showSecurityErrorMessage();
                }
            });
        },

        /**
         * معالجة أخطاء الخادم
         */
        handleServerError: function(xhr, settings) {
            console.log('تم اكتشاف خطأ في الخادم، محاولة إعادة الإرسال...');
            
            var self = this;
            setTimeout(function() {
                self.retryAjaxRequest(settings);
            }, this.retryDelay);
        },

        /**
         * تجديد رمز الأمان
         */
        refreshNonce: function(callback) {
            if (this.nonceRefreshInProgress) {
                setTimeout(function() {
                    callback(false);
                }, 100);
                return;
            }

            this.nonceRefreshInProgress = true;
            var self = this;

            $.ajax({
                url: ajaxurl || formElrakamiAdmin.ajaxurl,
                type: 'POST',
                data: {
                    action: 'form_elrakami_refresh_nonce'
                },
                success: function(response) {
                    if (response.success && response.data.nonce) {
                        // تحديث رمز الأمان في جميع المتغيرات
                        if (typeof formElrakamiAdmin !== 'undefined') {
                            formElrakamiAdmin.nonce = response.data.nonce;
                        }
                        if (typeof formElrakami !== 'undefined') {
                            formElrakami.nonce = response.data.nonce;
                        }
                        
                        console.log('تم تجديد رمز الأمان بنجاح');
                        callback(true);
                    } else {
                        console.error('فشل في الحصول على رمز أمان جديد');
                        callback(false);
                    }
                },
                error: function() {
                    console.error('خطأ في طلب تجديد رمز الأمان');
                    callback(false);
                },
                complete: function() {
                    self.nonceRefreshInProgress = false;
                }
            });
        },

        /**
         * إعادة محاولة طلب AJAX
         */
        retryAjaxRequest: function(originalSettings) {
            // تحديث رمز الأمان في البيانات المرسلة
            if (originalSettings.data) {
                if (typeof originalSettings.data === 'string') {
                    // تحديث nonce في البيانات النصية
                    originalSettings.data = originalSettings.data.replace(
                        /nonce=[^&]*/,
                        'nonce=' + (formElrakamiAdmin.nonce || formElrakami.nonce)
                    );
                } else if (typeof originalSettings.data === 'object') {
                    // تحديث nonce في البيانات الكائنية
                    originalSettings.data.nonce = formElrakamiAdmin.nonce || formElrakami.nonce;
                }
            }

            // إعادة إرسال الطلب
            $.ajax(originalSettings);
        },

        /**
         * إعداد تجديد رمز الأمان التلقائي
         */
        setupNonceRefresh: function() {
            var self = this;
            
            // تجديد رمز الأمان كل 10 دقائق
            setInterval(function() {
                self.refreshNonce(function(success) {
                    if (success) {
                        console.log('تم تجديد رمز الأمان تلقائياً');
                    }
                });
            }, 10 * 60 * 1000); // 10 دقائق
        },

        /**
         * عرض رسالة خطأ الأمان
         */
        showSecurityErrorMessage: function() {
            var message = 'حدث خطأ في الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.';
            
            // البحث عن عنصر لعرض الرسالة
            var $messageContainer = $('.form-elrakami-message, .notice, .error').first();
            
            if ($messageContainer.length === 0) {
                // إنشاء عنصر جديد لعرض الرسالة
                $messageContainer = $('<div class="notice notice-error"><p></p></div>');
                $('body').prepend($messageContainer);
            }
            
            $messageContainer.find('p').text(message);
            $messageContainer.show();
            
            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(function() {
                $messageContainer.fadeOut();
            }, 5000);
        },

        /**
         * عرض رسالة خطأ الخادم
         */
        showServerErrorMessage: function() {
            var message = 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.';
            this.showErrorMessage(message);
        },

        /**
         * عرض رسالة خطأ عامة
         */
        showErrorMessage: function(message) {
            console.error(message);
            
            // يمكن إضافة المزيد من طرق عرض الرسائل هنا
            if (typeof toastr !== 'undefined') {
                toastr.error(message);
            } else {
                alert(message);
            }
        }
    };

    // تهيئة معالج الأمان عند تحميل الصفحة
    $(document).ready(function() {
        securityHandler.init();
    });

    // تصدير معالج الأمان للاستخدام العام
    window.FormElrakamiSecurityHandler = securityHandler;

})(jQuery);
