<?php

/**
 * Form Elrakami
 *
 * @link              https://elrakami.com
 * @since             1.2.4
 * @package           Form_Elrakami
 *
 * @wordpress-plugin
 * Plugin Name:       Form Elrakami
 * Plugin URI:        https://elrakami.com/plugins/form-elrakami
 * Description:       إضافة لإنشاء نماذج مخصصة لجمع معلومات العملاء عند طلب المنتجات في ووكومرس
 * Version:           1.2.4
 * Update URI: https://api.freemius.com
 * Author:            Elrakami
 * Author URI:        https://elrakami.com
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       form-elrakami
 * Domain Path:       /languages
 */
// If this file is called directly, abort.
if ( !defined( 'WPINC' ) ) {
    die;
}
/**
 * Current plugin version.
 */
define( 'FORM_ELRAKAMI_VERSION', '1.2.4' );

/**
 * Plugin directory path.
 */
define( 'FORM_ELRAKAMI_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
/**
 * تكامل Freemius
 */
if ( !function_exists( 'fe_fs' ) ) {
    // إنشاء دالة مساعدة للوصول السهل إلى SDK
    function fe_fs() {
        global $fe_fs;
        if ( !isset( $fe_fs ) ) {
            // التحقق من وجود مجلد freemius
            $freemius_sdk_path = dirname( __FILE__ ) . '/freemius/start.php';
            // إذا لم يكن مجلد freemius موجودًا، لا تقم بتحميل SDK
            if ( !file_exists( $freemius_sdk_path ) ) {
                return false;
            }
            // تضمين Freemius SDK
            require_once $freemius_sdk_path;
            $fe_fs = fs_dynamic_init( array(
                'id'               => '18921',
                'slug'             => 'form-elrakami',
                'type'             => 'plugin',
                'public_key'       => 'pk_71c61c96e2bfdcca2fed176b70b3b',
                'is_premium'       => true,
                'is_premium_only'  => true,
                'has_addons'       => false,
                'has_paid_plans'   => true,
                'is_org_compliant' => false,
                'menu'             => array(
                    'first-path' => 'plugins.php',
                    'support'    => false,
                ),
                'is_live'          => true,
            ) );
        }
        return $fe_fs;
    }

    // تهيئة Freemius
    fe_fs();
    // إشارة إلى أن SDK تم تحميله
    do_action( 'fe_fs_loaded' );
    // إضافة إجراء التنظيف بعد إلغاء التثبيت
    fe_fs()->add_action( 'after_uninstall', 'fe_fs_uninstall_cleanup' );
}
/**
 * The code that runs during plugin activation.
 */
function activate_form_elrakami() {
    require_once plugin_dir_path( __FILE__ ) . 'includes/class-form-elrakami-activator.php';
    Form_Elrakami_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_form_elrakami() {
    require_once plugin_dir_path( __FILE__ ) . 'includes/class-form-elrakami-deactivator.php';
    Form_Elrakami_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_form_elrakami' );
register_deactivation_hook( __FILE__, 'deactivate_form_elrakami' );
/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-form-elrakami.php';
/**
 * الجزائر بيانات المدن والبلديات
 */
require plugin_dir_path( __FILE__ ) . 'includes/algeria-cities.php';
/**
 * The helper class with utility functions.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-form-elrakami-helper.php';
/**
 * ميتا بوكس المتغيرات والإعدادات في صفحة المنتج
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-product-variation-metabox.php';

/**
 * ميتا بوكس تعطيل نموذج الطلب للمنتج
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-form-disable-metabox.php';

/**
 * ميتا بوكس إعدادات السلة للمنتج
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-cart-metabox.php';

/**
 * ميتا بوكس رسوم التوصيل الإضافية للمنتجات
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-shipping-cost-metabox.php';

/**
 * نظام السلة المخصص
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-custom-cart-system.php';

// تم إزالة ميتا بوكس العروض
/**
 * تخصيص Freemius وإدارة التراخيص
 */
require plugin_dir_path( __FILE__ ) . 'includes/freemius-customization.php';

/**
 * مدير تحديث فئات الشحن
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-shipping-classes-updater.php';

/**
 * ملفات الاختبارات (في بيئة الإدارة فقط)
 */
if (is_admin()) {
    require plugin_dir_path( __FILE__ ) . 'tests/test-shipping-classes.php';
    require plugin_dir_path( __FILE__ ) . 'tests/test-additional-shipping-costs.php';
}
/**
 * Check for WooCommerce and license before initializing the plugin
 */
function form_elrakami_check_woocommerce() {
    if ( !class_exists( 'WooCommerce' ) ) {
        add_action( 'admin_notices', 'form_elrakami_woocommerce_notice' );
        return;
    }
    // التحقق من صلاحية الترخيص
    if ( function_exists( 'form_elrakami_check_license' ) && !form_elrakami_check_license() ) {
        // لا تقم بتشغيل الإضافة إذا كان الترخيص غير صالح
        return;
    }
    /**
     * Begins execution of the plugin.
     *
     * @since    1.0.0
     */
    function run_form_elrakami() {
        $plugin = new Form_Elrakami();
        $plugin->run();
    }

    run_form_elrakami();
}

add_action( 'plugins_loaded', 'form_elrakami_check_woocommerce' );
/**
 * Notice for WooCommerce requirement
 */
function form_elrakami_woocommerce_notice() {
    ?>
    <div class="error">
        <p><?php
    _e( 'Form Elrakami requires WooCommerce to be installed and activated.', 'form-elrakami' );
    ?></p>
    </div>
    <?php
}

/**
 * دالة لحذف مجلد وكل محتوياته بشكل متكرر
 *
 * @param string $dir مسار المجلد المراد حذفه
 * @return bool نجاح أو فشل العملية
 */
function form_elrakami_delete_directory( $dir ) {
    if ( !file_exists( $dir ) ) {
        return true;
    }

    if ( !is_dir( $dir ) ) {
        return unlink( $dir );
    }

    foreach ( scandir( $dir ) as $item ) {
        if ( $item == '.' || $item == '..' ) {
            continue;
        }

        if ( !form_elrakami_delete_directory( $dir . DIRECTORY_SEPARATOR . $item ) ) {
            return false;
        }
    }

    return rmdir( $dir );
}

/**
 * دالة التنظيف بعد إلغاء تثبيت الإضافة
 * تُستدعى بواسطة Freemius بعد إرسال بيانات إلغاء التثبيت إلى الخادم
 */
function fe_fs_uninstall_cleanup() {
    // التحقق مما إذا كان يجب حذف البيانات
    $delete_data = get_option( 'form_elrakami_delete_data' );
    if ( $delete_data ) {
        global $wpdb;

        // تنظيف مناطق الشحن الجزائرية أولاً
        form_elrakami_cleanup_shipping_zones_on_uninstall();

        // حذف الجداول
        $wpdb->query( "DROP TABLE IF EXISTS {$wpdb->prefix}form_elrakami_forms" );
        $wpdb->query( "DROP TABLE IF EXISTS {$wpdb->prefix}form_elrakami_submissions" );

        // حذف جميع خيارات الإضافة
        $options_to_delete = array(
            // خيارات أساسية
            'form_elrakami_version',
            'form_elrakami_default_form_id',
            'form_elrakami_delete_data',
            'form_elrakami_shipping_companies_data',
            'form_elrakami_deleted_companies',
            'form_elrakami_notification_email',
            'form_elrakami_container_style',
            'form_elrakami_settings',
            'form_elrakami_woo_location',
            
            // خيارات النموذج
            'form_elrakami_title_text',
            'form_elrakami_description_text',
            'form_elrakami_require_fields',
            'form_elrakami_language',
            'form_elrakami_send_customer_email',
            
            // خيارات التحقق من رقم الهاتف
            'form_elrakami_phone_validation_enabled',
            'form_elrakami_phone_prefixes',
            'form_elrakami_phone_length',
            'form_elrakami_custom_phone_validation',
            
            // خيارات تقييد الطلبات
            'form_elrakami_limit_orders_enabled',
            'form_elrakami_max_orders',
            'form_elrakami_time_period',
            
            // خيارات متقدمة
            'form_elrakami_disable_autocomplete',
            'form_elrakami_disable_copy_paste',
            'form_elrakami_save_abandoned_orders',
            'form_elrakami_abandoned_order_status',
            
            // خيارات إضافية
            'shipping_companies_data',
            'form_elrakami_shipping_class_costs',
            'fs_debug_mode',
            'fs_storage_logger',
            'fs_active_plugins'
        );
        
        // حذف خيارات WooCommerce المرتبطة بالإضافة
        // استعادة إعدادات WooCommerce الافتراضية إذا تم تعديلها بواسطة الإضافة
        if (function_exists('WC')) {
            // استرجاع جميع معرفات مناطق الشحن المسطحة التي تم إنشاؤها بواسطة الإضافة
            $shipping_zones = WC_Shipping_Zones::get_zones();
            foreach ($shipping_zones as $zone_data) {
                $zone = new WC_Shipping_Zone($zone_data['id']);
                $shipping_methods = $zone->get_shipping_methods();
                foreach ($shipping_methods as $instance_id => $method) {
                    if ($method->id === 'flat_rate') {
                        delete_option('woocommerce_flat_rate_' . $instance_id . '_settings');
                    }
                }
            }
        }
        
        // حذف جميع الخيارات
        foreach ( $options_to_delete as $option ) {
            delete_option( $option );
        }
        
        // حذف الكاش المخزن
        wp_cache_flush();
        
        // حذف البيانات المؤقتة
        $upload_dir = wp_upload_dir();
        $form_elrakami_dir = $upload_dir['basedir'] . '/form-elrakami';
        if ( file_exists( $form_elrakami_dir ) && is_dir( $form_elrakami_dir ) ) {
            form_elrakami_delete_directory( $form_elrakami_dir );
        }

        // إذا كان الموقع متعدد، قم بحذف البيانات لجميع المواقع
        if ( is_multisite() ) {
            $sites = get_sites();
            foreach ( $sites as $site ) {
                switch_to_blog( $site->blog_id );
                
                // حذف جميع الخيارات لكل موقع
                foreach ( $options_to_delete as $option ) {
                    delete_option( $option );
                }
                
                // حذف الكاش المخزن لكل موقع
                wp_cache_flush();
                
                restore_current_blog();
            }
        }
        
        // حذف البيانات المخزنة في جدول الخيارات المؤقتة
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_form_elrakami_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_timeout_form_elrakami_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_orders_history_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_timeout_orders_history_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_fs_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_timeout_fs_%'" );
        
        // حذف البيانات المخزنة في جدول postmeta
        $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE '_form_elrakami_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key = '_form_settings'" );

        // حذف رسوم التوصيل الإضافية من جميع المنتجات
        $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key = '_form_elrakami_additional_shipping_cost'" );
        $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key = '_form_elrakami_shipping_cost_description'" );
        
        // حذف أي طلبات مسودة تم إنشاؤها بواسطة الإضافة
        $draft_orders = $wpdb->get_col(
            "SELECT ID FROM {$wpdb->posts} 
            WHERE post_type = 'shop_order' 
            AND post_status = 'draft' 
            AND EXISTS (SELECT * FROM {$wpdb->postmeta} 
                       WHERE post_id = {$wpdb->posts}.ID 
                       AND meta_key = '_form_elrakami_form_data')"
        );
        
        if (!empty($draft_orders)) {
            foreach ($draft_orders as $order_id) {
                wp_delete_post($order_id, true);
            }
        }
    }
}

/**
 * تنظيف مناطق الشحن الجزائرية عند إلغاء التثبيت
 */
function form_elrakami_cleanup_shipping_zones_on_uninstall() {
    if (!class_exists('WC_Shipping_Zones')) {
        return;
    }

    global $wpdb;

    try {
        // الحصول على جميع معرفات طرق الشحن المرتبطة بالولايات الجزائرية
        $shipping_method_ids = $wpdb->get_col("
            SELECT DISTINCT szm.instance_id
            FROM {$wpdb->prefix}woocommerce_shipping_zone_methods szm
            INNER JOIN {$wpdb->prefix}woocommerce_shipping_zone_locations szl
            ON szm.zone_id = szl.zone_id
            WHERE szl.location_code LIKE 'DZ:DZ-%'
        ");

        // حذف مناطق الشحن الجزائرية
        $zone_ids = $wpdb->get_col("
            SELECT DISTINCT zone_id
            FROM {$wpdb->prefix}woocommerce_shipping_zone_locations
            WHERE location_code LIKE 'DZ:DZ-%'
        ");

        foreach ($zone_ids as $zone_id) {
            $zone = new WC_Shipping_Zone($zone_id);
            $zone->delete();
        }

        // تنظيف خيارات طرق الشحن
        foreach ($shipping_method_ids as $instance_id) {
            delete_option('woocommerce_flat_rate_' . $instance_id . '_settings');
        }

        // تنظيف أي بيانات متبقية
        $wpdb->query("
            DELETE FROM {$wpdb->prefix}woocommerce_shipping_zone_locations
            WHERE location_code LIKE 'DZ:DZ-%'
        ");

        // إعادة تعيين إعدادات الدول المسموحة
        delete_option('woocommerce_allowed_countries');
        delete_option('woocommerce_specific_allowed_countries');

    } catch (Exception $e) {
        error_log('Form Elrakami: خطأ في تنظيف مناطق الشحن عند إلغاء التثبيت - ' . $e->getMessage());
    }
}
