/**
 * تحديثات تخطيط النموذج
 * CSS إضافي لدعم خيارات تخطيط النموذج الجديدة
 *
 * يتضمن إعدادات ارتفاع الحقول الجديدة
 */

/* تخطيط الأعمدة العام */
.form-elrakami-form.columns-layout .form-elrakami-fields {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 15px;
}

/* تخطيط العمودين لكل أحجام الشاشات */
.form-elrakami-form.all-screens-columns .form-elrakami-fields {
    grid-template-columns: repeat(2, 1fr);
}

/* تخطيط العمودين للشاشات الكبيرة فقط */
@media screen and (max-width: 768px) {
    .form-elrakami-form.desktop-only-columns .form-elrakami-fields {
        grid-template-columns: 1fr;
    }
}

/* إعدادات حجم الفراغات بين الحقول */
.form-elrakami-form.gap-small .form-elrakami-fields {
    grid-gap: 8px;
}

.form-elrakami-form.gap-medium .form-elrakami-fields {
    grid-gap: 15px;
}

.form-elrakami-form.gap-large .form-elrakami-fields {
    grid-gap: 25px;
}

/* جعل الحقل الأخير يأخذ العرض الكامل عندما يكون وحيداً في الصف الأخير */
.form-elrakami-form.columns-layout .form-elrakami-field:last-child:nth-child(odd) {
    grid-column: 1 / -1;
}

/* تحسين حقل العنوان لأخذ العرض الكامل دائما */
.form-elrakami-form.columns-layout .form-elrakami-field[data-field-type="detailed_address"] {
    grid-column: 1 / -1;
}

/* تحسينات لحقول خاصة في نظام الأعمدة */
.form-elrakami-form.columns-layout .form-elrakami-field textarea {
    min-height: 120px;
}

/* تحسينات الخطوط والهوامش */
.form-elrakami-form p.field-description {
    font-size: 12px;
    color: #777;
    margin-top: 5px;
}

/* معالجة توحيد ارتفاع أيقونات الحقول مع الحقول نفسها */
.form-elrakami-form .input-group {
    display: flex;
    align-items: stretch;
    width: 100%;
}

/* إعدادات ارتفاع الحقول الافتراضية (متوسط) */
.form-elrakami-form .input-group-text,
.form-elrakami-form .form-control,
.form-elrakami-form .form-select {
    height: 38px !important; /* ارتفاع متوسط (افتراضي) */
    min-height: 38px !important;
    line-height: 1.5;
    box-sizing: border-box;
    border-width: 1px;
    vertical-align: middle;
    transition: all 0.2s ease;
    font-size: 13px !important;
}

/* إعدادات ارتفاع الحقول المنخفضة */
.form-elrakami-form.field-height-small .input-group-text,
.form-elrakami-form.field-height-small .form-control,
.form-elrakami-form.field-height-small .form-select {
    height: 32px !important;
    min-height: 32px !important;
    font-size: 13px;
}

/* إعدادات ارتفاع الحقول المرتفعة */
.form-elrakami-form.field-height-large .input-group-text,
.form-elrakami-form.field-height-large .form-control,
.form-elrakami-form.field-height-large .form-select {
    height: 46px !important;
    min-height: 46px !important;
    font-size: 15px;
}

/* إعدادات ارتفاع الحقول الكبيرة */
.form-elrakami-form.field-height-xlarge .input-group-text,
.form-elrakami-form.field-height-xlarge .form-control,
.form-elrakami-form.field-height-xlarge .form-select {
    height: 52px !important;
    min-height: 52px !important;
    font-size: 16px;
}

.form-elrakami-form .form-control,
.form-elrakami-form .form-select {
    padding: 0.5rem 0.75rem;
    font-size: 13px;
}

/* تعديل حجم خط مربعات الإدخال بناءً على الارتفاع */
.form-elrakami-form.field-height-small .form-control,
.form-elrakami-form.field-height-small .form-select {
    padding: 0.4rem 0.6rem;
    font-size: 12px;
}

.form-elrakami-form.field-height-large .form-control,
.form-elrakami-form.field-height-large .form-select {
    padding: 0.6rem 0.85rem;
    font-size: 14px;
}

.form-elrakami-form.field-height-xlarge .form-control,
.form-elrakami-form.field-height-xlarge .form-select {
    padding: 0.7rem 1rem;
}

.form-elrakami-form .input-group-text {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 0.75rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    background-color: #4069f2;
    color: white;
    border-color: #4069f2;
    width: 38px; /* عرض افتراضي (متوسط) */
    font-size: 14px;
    margin: 0;
}

/* تعديل عرض مربعات الأيقونات بناءً على ارتفاع الحقول */
.form-elrakami-form.field-height-small .input-group-text {
    width: 32px;
    font-size: 13px;
}

.form-elrakami-form.field-height-large .input-group-text {
    width: 46px;
    font-size: 14px;
}

.form-elrakami-form.field-height-xlarge .input-group-text {
    width: 52px;
    font-size: 15px;
}

/* تحسين توافق الحقول والأيقونات معًا */
.form-elrakami-form .input-group .form-control,
.form-elrakami-form .input-group .form-select {
    flex: 1;
    min-height: 38px; /* الارتفاع المتوسط الافتراضي */
    height: 38px; /* تأكيد على الارتفاع الافتراضي */
    border-right-width: 0;
    border-left-width: 1px;
    box-sizing: border-box;
}

/* تحديد الارتفاع المناسب للحقول حسب فئة الارتفاع */
.form-elrakami-form.field-height-small .input-group .form-control,
.form-elrakami-form.field-height-small .input-group .form-select {
    min-height: 32px !important;
    height: 32px !important;
}

.form-elrakami-form.field-height-large .input-group .form-control,
.form-elrakami-form.field-height-large .input-group .form-select {
    min-height: 46px !important;
    height: 46px !important;
}

.form-elrakami-form.field-height-xlarge .input-group .form-control,
.form-elrakami-form.field-height-xlarge .input-group .form-select {
    min-height: 52px !important;
    height: 52px !important;
}

/* تنسيقات مفتاح التبديل بين إظهار وإخفاء أيقونات الحقول */
.form-elrakami-form.hide-icons .input-group-text {
    display: none !important;
    width: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    overflow: hidden !important;
    position: absolute !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

.form-elrakami-form.hide-icons .input-group .form-control,
.form-elrakami-form.hide-icons .input-group .form-select {
    border-radius: 4px !important;
    width: 100% !important;
    border-width: 1px !important;
    border-right-width: 1px !important;
    border-left-width: 1px !important;
}

/* تصحيح تنسيق المدخلات في حالة إخفاء الأيقونات */
.form-elrakami-form.hide-icons .form-control,
.form-elrakami-form.hide-icons .form-select {
    width: 100%;
}

/* تأكيد إخفاء الأيقونات بأكثر من طريقة */
.form-elrakami-form.hide-icons .input-group {
    position: relative;
}

/* تحسين توافق الحقول مع جميع المتصفحات */
.form-elrakami-form .input-group-text,
.form-elrakami-form input.form-control,
.form-elrakami-form select.form-select {
    box-sizing: border-box !important;
    margin: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    display: flex !important;
    align-items: center !important;
}

/* تعديل الأنماط لضمان توافق متساوٍ للارتفاع في أي متصفح */
.form-elrakami-form .input-group {
    display: flex !important;
    align-items: stretch !important;
}

/* تغطية حالات الارتفاع غير المتوافقة - حل للمتصفحات التي تتجاوز الأنماط الأصلية */
.form-elrakami-form input.form-control:not([type="checkbox"]):not([type="radio"]),
.form-elrakami-form select.form-select {
    line-height: normal !important;
}

/* حل مشكلة ارتفاع حقول select على Safari وiOS */
.form-elrakami-form select.form-select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path fill="%23333" d="M6 8.825L1.175 4 0 5.175 6 11.175 12 5.175 10.825 4z"/></svg>') !important;
    background-repeat: no-repeat !important;
    background-position: left 10px center !important;
    background-size: 10px !important;
    padding-left: 30px !important;
}

/* ضمان تطبيق الارتفاع المناسب على حقول select */
.form-elrakami-form.field-height-small select.form-select {
    background-position: left 8px center !important;
}

.form-elrakami-form.field-height-large select.form-select {
    background-position: left 12px center !important;
}

.form-elrakami-form.field-height-xlarge select.form-select {
    background-position: left 14px center !important;
    background-size: 12px !important;
}

/* تعديلات خاصة بحقول textarea حسب ارتفاع الحقول */
.form-elrakami-form textarea.form-control {
    height: auto;
    min-height: 120px;
}

/* تعديل padding لمناطق النص حسب ارتفاع الحقول */
.form-elrakami-form.field-height-small textarea.form-control {
    padding: 8px 10px;
}

.form-elrakami-form.field-height-medium textarea.form-control {
    padding: 10px 12px;
}

.form-elrakami-form.field-height-large textarea.form-control {
    padding: 12px 15px;
    min-height: 130px;
}

.form-elrakami-form.field-height-xlarge textarea.form-control {
    padding: 15px 18px;
    min-height: 140px;
    font-size: 16px;
}

/* تحسين عرض حقول الاختيار المتعدد والزر الراديو في الأعمدة */
.form-elrakami-form.columns-layout .checkbox-options,
.form-elrakami-form.columns-layout .radio-options {
    display: flex;
    flex-direction: column;
    padding: 8px 0;
}

.form-elrakami-form.columns-layout .checkbox-label,
.form-elrakami-form.columns-layout .radio-label {
    margin-bottom: 8px;
}

/* تنسيق خاص للأعمدة عندما يكون الفراغ صغيرًا */
.form-elrakami-form.gap-small .checkbox-label,
.form-elrakami-form.gap-small .radio-label {
    font-size: 13px;
}

/* تحسين إظهار الحقول المطلوبة */
.form-elrakami-form .required {
    color: #e32;
    font-weight: bold;
    margin-right: 3px;
}

/* تحسين التوافق مع الهواتف الصغيرة جداً */
@media screen and (max-width: 480px) {
    /* تم تعديله ليظهر عمودين في الهاتف أيضاً عند اختيار "أعمدة (كل الشاشات)" */
    .form-elrakami-form.desktop-only-columns .form-elrakami-fields {
        grid-template-columns: 1fr;
    }

    /* تأكيد على تطبيق ارتفاعات الحقول على الأجهزة المحمولة */
    .form-elrakami-form .form-control,
    .form-elrakami-form .form-select,
    .form-elrakami-form .input-group-text {
        -webkit-appearance: none;
        appearance: none;
    }

    .form-elrakami-form.field-height-small .form-control,
    .form-elrakami-form.field-height-small .form-select,
    .form-elrakami-form.field-height-small .input-group-text {
        height: 32px !important;
        min-height: 32px !important;
    }

    .form-elrakami-form.field-height-medium .form-control,
    .form-elrakami-form.field-height-medium .form-select,
    .form-elrakami-form.field-height-medium .input-group-text {
        height: 38px !important;
        min-height: 38px !important;
    }

    .form-elrakami-form.field-height-large .form-control,
    .form-elrakami-form.field-height-large .form-select,
    .form-elrakami-form.field-height-large .input-group-text {
        height: 46px !important;
        min-height: 46px !important;
    }

    .form-elrakami-form.field-height-xlarge .form-control,
    .form-elrakami-form.field-height-xlarge .form-select,
    .form-elrakami-form.field-height-xlarge .input-group-text {
        height: 52px !important;
        min-height: 52px !important;
    }
}


/* تأكيد تناسق الأيقونات مع حقول الولاية والبلدية */
.form-elrakami-form .input-group:has(#state) .input-group-text,
.form-elrakami-form .input-group:has(#municipality) .input-group-text {
    height: inherit !important;
    min-height: inherit !important;
    display: flex !important;
    align-items: center !important;
}

/* ضمان تطبيق التنسيق المناسب على فئة مختلفة من حقول الإدخال */
.form-elrakami-form .input-group select.form-select {
    padding-right: 8px !important;
    text-overflow: ellipsis;
}

/* تنسيق الرسائل التحذيرية في حالة الأخطاء */
.form-elrakami-form .field-error {
    color: #e32;
    font-size: 12px;
    margin-top: 5px;
    font-weight: 500;
}