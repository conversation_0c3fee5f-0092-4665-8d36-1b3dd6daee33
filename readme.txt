=== Form Elrakami ===
Contributors: elrakami
Tags: woocommerce, forms, shipping, checkout
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.2
Stable tag: 1.0.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

إضافة لإنشاء نماذج مخصصة لجمع معلومات العملاء عند طلب المنتجات في ووكومرس.

== الوصف ==

Form Elrakami هي إضافة قوية تتيح لك إنشاء نماذج مخصصة لجمع معلومات العملاء عند طلب المنتجات في متجر WooCommerce الخاص بك.

= الميزات الرئيسية =

* نظام تحرير حقول متطور مع حفظ فوري
* تكامل كامل مع نظام الشحن في WooCommerce
* ترتيب تلقائي للولايات الجزائرية
* واجهة مستخدم سهلة وحديثة
* أداء محسن وموثوقية عالية

= نظام تحرير الحقول =

* تحرير مدمج داخل الحقل بدون نوافذ منبثقة
* حفظ فوري للتغييرات في قاعدة البيانات
* تخصيص كامل لإعدادات الحقول
* التحكم في الإلزامية والرؤية

= التكامل مع WooCommerce =

* دعم كامل لطرق الشحن
* تحويل تلقائي لأكواد الولايات
* طرق شحن افتراضية كاحتياطي
* تنظيم منطقي للمناطق

= تحسينات الأداء =

* حفظ فوري عبر AJAX
* تحميل سريع للصفحات
* استهلاك أقل للذاكرة
* معالجة محسنة للأخطاء

== التثبيت ==

1. قم بتحميل الإضافة إلى مجلد `/wp-content/plugins/`
2. قم بتفعيل الإضافة من لوحة التحكم
3. قم بتثبيت وتفعيل WooCommerce إذا لم يكن مثبتاً
4. قم بتثبيت Freemius SDK (راجع الوثائق للتعليمات)
5. قم بتفعيل الترخيص

== الأسئلة الشائعة ==

= هل الإضافة متوافقة مع آخر إصدار من WooCommerce؟ =
نعم، الإضافة متوافقة مع آخر إصدار من WooCommerce ويتم تحديثها باستمرار.

= هل يمكنني تخصيص حقول النموذج؟ =
نعم، يمكنك تخصيص جميع جوانب الحقول بما في ذلك العنوان والنوع والإلزامية والرؤية.

= هل تدعم الإضافة الشحن في الجزائر؟ =
نعم، تم تصميم الإضافة خصيصاً للعمل مع نظام الشحن في الجزائر مع دعم كامل للولايات.

== التحديثات ==

* نظام تحرير حقول جديد كلياً
* تحسينات في التكامل مع WooCommerce
* إصلاح مشكلات ترتيب الولايات
* تحسينات في الأداء والموثوقية

== التحديثات القادمة ==

* نظام نسخ حقول محسن
* وظائف تراجع/إعادة
* معاينة مباشرة للتغييرات
* قوالب حقول جاهزة
* نظام تصدير/استيراد

== الدعم ==

للحصول على الدعم الفني، يرجى التواصل عبر:
<EMAIL>
