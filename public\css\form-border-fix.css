/**
 * إصلاح مشكلة ظهور واختفاء الحدود السريع عند إعداد حقول النموذج
 */

/* إضافة تأثير انتقالي أكثر سلاسة للحدود */
.form-elrakami-field input,
.form-elrakami-field select,
.form-elrakami-field textarea {
    transition: border-color 0.3s ease, box-shadow 0.3s ease !important;
    border: 1px solid var(--form-input-border, #3730a3) !important;
}

/* تحسين تأثير التركيز على الحقول */
.form-elrakami-field input:focus,
.form-elrakami-field select:focus,
.form-elrakami-field textarea:focus {
    border-color: var(--form-primary-color, #3498db) !important;
    box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.3) !important;
    outline: none !important;
}

/* منع تغيير حجم الحقول عند التركيز */
.form-elrakami-field input,
.form-elrakami-field select,
.form-elrakami-field textarea {
    box-sizing: border-box !important;
}

/* تثبيت حجم الحقول لمنع التغيير المفاجئ */
.form-elrakami-field .input-group {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
    width: 100%;
}

/* تثبيت أيقونات الحقول */
.form-elrakami-field .field-icon {
    position: absolute;
    top: 0;
    right: 0;
    height: 100% !important;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    pointer-events: none;
}

/* تحسين مظهر الحقول عند التحويم */
.form-elrakami-field input:hover,
.form-elrakami-field select:hover,
.form-elrakami-field textarea:hover {
    border-color: var(--form-input-focus-border, #93c5fd) !important;
}

/* تثبيت حجم الحقول في جميع الحالات */
.form-elrakami-field input,
.form-elrakami-field select {
    height: 38px !important;
    line-height: 38px !important;
    padding: 0 10px !important;
    width: 100% !important;
    margin: 0 !important;
}

/* تثبيت حجم مناطق النص */
.form-elrakami-field textarea {
    min-height: 80px !important;
    padding: 10px !important;
    width: 100% !important;
    margin: 0 !important;
}

/* تحسين مظهر الحقول المعطلة */
.form-elrakami-field input:disabled,
.form-elrakami-field select:disabled,
.form-elrakami-field textarea:disabled {
    background-color: #f1f1f1 !important;
    color: #888 !important;
    cursor: not-allowed !important;
    opacity: 0.7;
}

/* تحسين مظهر الحقول غير الصالحة */
.form-elrakami-field input.error,
.form-elrakami-field select.error,
.form-elrakami-field textarea.error {
    border-color: var(--form-error-color, #ef4444) !important;
    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3) !important;
}

/* تحسين مظهر الحقول الصالحة */
.form-elrakami-field input.valid,
.form-elrakami-field select.valid,
.form-elrakami-field textarea.valid {
    border-color: var(--form-success-color, #10b981) !important;
    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.3) !important;
}
