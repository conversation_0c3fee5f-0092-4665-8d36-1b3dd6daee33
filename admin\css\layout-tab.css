/* تنسيقات القائمة الجانبية الرئيسية */
.form-settings-container {
    display: flex;
    gap: 15px;
    margin: 15px 0;
    padding: 12px;
    background: #f0f0f1;
    border-radius: 6px;
}

/* القائمة الجانبية */
.settings-sidebar {
    flex: 0 0 200px;
    background: #fff;
    border-radius: 4px;
    padding: 10px 0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    position: sticky;
    top: 32px;
    height: fit-content;
    border: 1px solid #e2e4e7;
}

.settings-navigation {
    margin: 0;
    padding: 0;
    list-style: none;
}

.settings-navigation li {
    margin: 0;
    padding: 0;
}

.settings-navigation a {
    display: block;
    padding: 8px 12px;
    color: #1d2327;
    text-decoration: none;
    border-right: 3px solid transparent;
    transition: all 0.2s ease;
    font-size: 13px;
}

.settings-navigation a:hover {
    background: #f6f7f7;
    border-right-color: #cdcdcd;
}

.settings-navigation a.active {
    background: #f0f6fc;
    border-right-color: #2271b1;
    color: #2271b1;
    font-weight: 500;
}

/* محتوى الإعدادات */
.settings-content {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    border: 1px solid #e2e4e7;
}

.settings-section {
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #eee;
}

.settings-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.settings-title {
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #2271b1;
    color: #1d2327;
    font-size: 1.1em;
}

/* تحسين عرض الحقول */
.settings-field {
    margin-bottom: 12px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e2e4e7;
}

.settings-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #1d2327;
    font-size: 13px;
}

.settings-field .description {
    margin-top: 3px;
    color: #646970;
    font-size: 12px;
}

/* تنسيق التنقل السلس */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 32px;
}

/* دعم RTL */
.rtl .settings-navigation a {
    border-right: none;
    border-left: 4px solid transparent;
}

.rtl .settings-navigation a:hover {
    border-left-color: #cdcdcd;
}

.rtl .settings-navigation a.active {
    border-left-color: #2271b1;
}

/* تنسيقات الشاشات الصغيرة */
@media screen and (max-width: 782px) {
    .form-settings-container {
        flex-direction: column;
    }

    .settings-sidebar {
        position: relative;
        top: 0;
        width: 100%;
        flex: none;
    }

    .settings-content {
        padding: 20px;
    }
}

/* تحسينات إضافية */
.settings-section h3 {
    color: #1d2327;
    font-size: 1em;
    margin: 1em 0 0.8em;
}

.settings-group {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

/* تنسيق أزرار التبديل */
.toggle-switch-container {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 6px 0;
}

.switch-toggle {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 18px;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .3s;
    border-radius: 18px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #2271b1;
}

input:checked + .toggle-slider:before {
    transform: translateX(18px);
}

.rtl input:checked + .toggle-slider:before {
    transform: translateX(-18px);
}

/* تنسيق حقول الإدخال */
.settings-field input[type="text"],
.settings-field input[type="number"],
.settings-field input[type="url"],
.settings-field select,
.settings-field textarea {
    width: 100%;
    max-width: 100%;
    margin: 0;
    box-sizing: border-box;
    padding: 4px 8px;
    height: 30px;
    font-size: 12px;
}

.settings-field textarea {
    height: auto;
    min-height: 60px;
}

.settings-field input[type="range"] {
    vertical-align: middle;
    width: 150px;
    height: 20px;
}

.settings-field output {
    margin-left: 8px;
    vertical-align: middle;
    font-size: 12px;
}

.rtl .settings-field output {
    margin-left: 0;
    margin-right: 8px;
}