/**
 * JavaScript للسلة المخصصة
 * Form Elrakami Custom Cart System
 */

jQuery(document).ready(function($) {
    'use strict';

    // متغيرات عامة
    const $cartSidebar = $('#cart-sidebar');
    const $floatingIcon = $('#floating-cart-icon');
    const $cartCount = $('.cart-count');
    const $cartItemsContainer = $('.cart-items-container');

    // تهيئة السلة
    initCart();

    /**
     * تهيئة السلة
     */
    function initCart() {
        // تحديث عداد السلة عند تحميل الصفحة
        updateCartDisplay();

        // ربط الأحداث
        bindEvents();

        // التحقق من حالة السلة وتحديث الفورم بعد تأخير قصير
        setTimeout(checkCartStatusAndUpdateForm, 500);
    }

    /**
     * ربط الأحداث
     */
    function bindEvents() {
        // فتح السلة عند النقر على الأيقونة العائمة
        $floatingIcon.on('click', openCartSidebar);

        // إغلاق السلة
        $cartSidebar.on('click', '.cart-close-btn, .cart-sidebar-overlay', closeCartSidebar);

        // منع إغلاق السلة عند النقر على المحتوى
        $cartSidebar.on('click', '.cart-sidebar-content', function(e) {
            e.stopPropagation();
        });

        // إضافة منتج إلى السلة
        $(document).on('click', '.form-elrakami-add-to-cart', handleAddToCart);

        // إضافة منتج إلى السلة من الأيقونة المدمجة
        $(document).on('click', '.cart-icon-inline', handleAddToCartInline);

        // تحديث الكمية
        $cartSidebar.on('click', '.qty-btn', handleQuantityChange);
        $cartSidebar.on('change', '.qty-input', handleQuantityInput);

        // حذف منتج من السلة
        $cartSidebar.on('click', '.remove-item', handleRemoveItem);

        // إتمام الطلب
        $cartSidebar.on('click', '.btn-checkout', handleCheckout);

        // متابعة التسوق
        $cartSidebar.on('click', '.btn-continue-shopping', closeCartSidebar);



        // منع إرسال النموذج عند النقر على زر السلة
        $(document).on('click', '.form-elrakami-add-to-cart', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });
    }

    /**
     * فتح السلة الجانبية
     */
    function openCartSidebar() {
        $cartSidebar.addClass('active');
        $('body').addClass('cart-sidebar-open');

        // تحديث محتويات السلة
        updateCartContents();
    }

    /**
     * إغلاق السلة الجانبية
     */
    function closeCartSidebar() {
        $cartSidebar.removeClass('active');
        $('body').removeClass('cart-sidebar-open');
    }

    /**
     * معالجة إضافة منتج إلى السلة
     */
    function handleAddToCart(e) {
        e.preventDefault();
        
        const $button = $(this);
        const $form = $button.closest('.form-elrakami-form');
        
        // التحقق من صحة النموذج
        if (!validateForm($form)) {
            showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // تعطيل الزر مؤقتاً
        $button.prop('disabled', true).text('جاري الإضافة...');

        // جمع بيانات النموذج
        const formData = collectFormData($form);
        
        // إضافة بيانات AJAX
        formData.append('action', 'add_to_custom_cart');
        formData.append('nonce', customCart.nonce);

        // إرسال الطلب
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(customCart.texts.added_to_cart, 'success');
                    updateCartCount(response.data.cart_count);

                    // تحديث حالة الفورم بعد إضافة المنتج
                    updateFormBasedOnCartStatus(false);

                    // فتح السلة تلقائياً
                    setTimeout(openCartSidebar, 500);
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(customCart.texts.error_occurred, 'error');
            },
            complete: function() {
                // إعادة تفعيل الزر
                const originalText = $button.data('original-text') || 'أضف إلى السلة';
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * معالجة إضافة منتج إلى السلة من الأيقونة المدمجة
     */
    function handleAddToCartInline(e) {
        e.preventDefault();
        e.stopPropagation();

        const $icon = $(this);
        const $form = $icon.closest('.form-elrakami-form');

        // التحقق من صحة النموذج
        if (!validateForm($form)) {
            showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // إضافة كلاس التحميل للأيقونة
        $icon.addClass('loading');

        // جمع بيانات النموذج
        const formData = collectFormData($form);

        // إضافة بيانات AJAX
        formData.append('action', 'add_to_custom_cart');
        formData.append('nonce', customCart.nonce);

        // إرسال الطلب
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(customCart.texts.added_to_cart, 'success');
                    updateCartCount(response.data.cart_count);

                    // تحديث حالة الفورم بعد إضافة المنتج
                    updateFormBasedOnCartStatus(false);

                    // فتح السلة تلقائياً
                    setTimeout(openCartSidebar, 500);
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(customCart.texts.error_occurred, 'error');
            },
            complete: function() {
                // إزالة كلاس التحميل
                $icon.removeClass('loading');
            }
        });
    }

    /**
     * جمع بيانات النموذج
     */
    function collectFormData($form) {
        const formData = new FormData();

        // جمع جميع حقول النموذج
        $form.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');
            const value = $field.val();

            if (name && value) {
                if ($field.attr('type') === 'checkbox' || $field.attr('type') === 'radio') {
                    if ($field.is(':checked')) {
                        formData.append(name, value);
                    }
                } else {
                    formData.append(name, value);
                }
            }
        });

        return formData;
    }

    /**
     * التحقق من صحة النموذج
     */
    function validateForm($form) {
        let isValid = true;

        // التحقق من الحقول المطلوبة المرئية فقط
        $form.find('[required]').each(function() {
            const $field = $(this);
            const $fieldContainer = $field.closest('.form-elrakami-fields, .form-elrakami-address-fields, .shipping-methods-container');

            // تجاهل الحقول المخفية
            if ($fieldContainer.length > 0 && !$fieldContainer.is(':visible')) {
                return true; // تخطي هذا الحقل
            }

            if (!$field.val() || $field.val().trim() === '') {
                $field.addClass('error');
                isValid = false;
            } else {
                $field.removeClass('error');
            }
        });

        return isValid;
    }

    /**
     * معالجة تغيير الكمية
     */
    function handleQuantityChange(e) {
        e.preventDefault();
        
        const $button = $(this);
        const $cartItem = $button.closest('.cart-item');
        const $qtyInput = $cartItem.find('.qty-input');
        const productId = $cartItem.data('product-id');
        const isPlus = $button.hasClass('qty-plus');
        
        let currentQty = parseInt($qtyInput.val()) || 1;
        let newQty = isPlus ? currentQty + 1 : Math.max(1, currentQty - 1);
        
        updateProductQuantity(productId, newQty, $qtyInput);
    }

    /**
     * معالجة إدخال الكمية مباشرة
     */
    function handleQuantityInput(e) {
        const $input = $(this);
        const $cartItem = $input.closest('.cart-item');
        const productId = $cartItem.data('product-id');
        const newQty = Math.max(1, parseInt($input.val()) || 1);
        
        updateProductQuantity(productId, newQty, $input);
    }

    /**
     * تحديث كمية المنتج
     */
    function updateProductQuantity(productId, quantity, $input) {
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'update_cart_quantity',
                nonce: customCart.nonce,
                product_id: productId,
                quantity: quantity
            },
            success: function(response) {
                if (response.success) {
                    $input.val(quantity);
                    updateCartDisplay();
                    showNotification(customCart.texts.cart_updated, 'success');
                    // تحديث حالة الفورم (السلة ما زالت تحتوي على منتجات)
                    updateFormBasedOnCartStatus(false);
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(customCart.texts.error_occurred, 'error');
            }
        });
    }

    /**
     * معالجة حذف منتج
     */
    function handleRemoveItem(e) {
        e.preventDefault();
        
        const $cartItem = $(this).closest('.cart-item');
        const productId = $cartItem.data('product-id');
        
        if (!confirm('هل أنت متأكد من حذف هذا المنتج من السلة؟')) {
            return;
        }

        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'remove_from_custom_cart',
                nonce: customCart.nonce,
                product_id: productId
            },
            success: function(response) {
                if (response.success) {
                    $cartItem.fadeOut(300, function() {
                        $(this).remove();
                        updateCartDisplay();
                        // تحديث حالة الفورم بعد حذف المنتج
                        setTimeout(function() {
                            updateFormBasedOnCartStatus();
                        }, 500);
                    });
                    showNotification(customCart.texts.removed_from_cart, 'success');
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(customCart.texts.error_occurred, 'error');
            }
        });
    }

    /**
     * معالجة إتمام الطلب
     */
    function handleCheckout(e) {
        e.preventDefault();
        
        const $button = $(this);
        $button.prop('disabled', true).text('جاري المعالجة...');

        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'complete_cart_order',
                nonce: customCart.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');

                    // إعادة تعيين السلة
                    updateCartCount(0);
                    closeCartSidebar();

                    // تحديث حالة الفورم بعد إتمام الطلب (السلة فارغة)
                    updateFormBasedOnCartStatus(true);

                    // إعادة توجيه إلى صفحة الطلب إذا كان متاحاً
                    if (response.data.redirect_url) {
                        setTimeout(function() {
                            window.location.href = response.data.redirect_url;
                        }, 1500);
                    }
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(customCart.texts.error_occurred, 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(customCart.texts.checkout);
            }
        });
    }

    /**
     * تحديث عرض السلة
     */
    function updateCartDisplay() {
        updateCartContents();
    }

    /**
     * تحديث محتويات السلة
     */
    function updateCartContents() {
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_cart_contents',
                nonce: customCart.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث HTML السلة
                    $cartItemsContainer.html(data.cart_html);

                    // تحديث المجاميع
                    $('.subtotal-amount').text(formatPrice(data.subtotal));
                    $('.shipping-amount').text(formatPrice(data.shipping));
                    $('.total-amount').text(formatPrice(data.total));

                    // تحديث العداد
                    updateCartCount(data.cart_count);



                    // إظهار/إخفاء أزرار الإجراءات
                    if (data.empty) {
                        $('.cart-actions').hide();
                    } else {
                        $('.cart-actions').show();
                    }

                    // تحديث حالة الفورم بناءً على حالة السلة
                    updateFormBasedOnCartStatus(data.empty);
                } else {
                    console.error('خطأ في تحديث محتويات السلة:', response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في AJAX:', error);
                showNotification('خطأ في تحديث السلة', 'error');
            }
        });
    }

    /**
     * تحديث عداد السلة
     */
    function updateCartCount(count) {
        $cartCount.text(count);
        
        if (count > 0) {
            $cartCount.removeClass('hidden');
        } else {
            $cartCount.addClass('hidden');
        }
    }

    /**
     * تنسيق السعر
     */
    function formatPrice(price) {
        return parseFloat(price).toLocaleString('ar-DZ', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' ' + customCart.currency;
    }

    /**
     * إظهار إشعار
     */
    function showNotification(message, type) {
        // إنشاء عنصر الإشعار إذا لم يكن موجوداً
        let $notification = $('.cart-notification');
        if ($notification.length === 0) {
            $notification = $('<div class="cart-notification"></div>');
            $('body').append($notification);
        }

        // تعيين نوع ومحتوى الإشعار
        $notification.removeClass('success error info').addClass(type);
        $notification.text(message);
        $notification.fadeIn();

        // إخفاء الإشعار بعد 3 ثوانٍ
        setTimeout(function() {
            $notification.fadeOut();
        }, 3000);
    }

    /**
     * التحقق من حالة السلة وتحديث الفورم
     */
    function checkCartStatusAndUpdateForm() {
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_cart_contents',
                nonce: customCart.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    updateFormBasedOnCartStatus(data.empty);
                }
            },
            error: function() {
                console.log('خطأ في التحقق من حالة السلة');
            }
        });
    }

    /**
     * تحديث الفورم بناءً على حالة السلة
     */
    function updateFormBasedOnCartStatus(isEmpty = null) {
        // إذا لم يتم تمرير حالة السلة، نحصل عليها من العداد
        if (isEmpty === null) {
            const cartCount = parseInt($cartCount.text()) || 0;
            isEmpty = cartCount === 0;
        }

        // العثور على جميع النماذج في الصفحة
        $('.form-elrakami-form').each(function() {
            const $form = $(this);

            if (isEmpty) {
                // السلة فارغة - إظهار جميع الحقول
                $form.removeClass('cart-has-items');
                $form.find('.form-elrakami-fields').show();
                $form.find('.form-elrakami-address-fields').show();
                $form.find('.shipping-methods-container').show();
                $form.find('.cart-items-message').remove();
            } else {
                // السلة تحتوي على منتجات - إخفاء حقول المعلومات الشخصية وطرق التوصيل
                $form.addClass('cart-has-items');
                $form.find('.form-elrakami-fields').hide();
                $form.find('.form-elrakami-address-fields').hide();
                $form.find('.shipping-methods-container').hide();

                // إظهار رسالة توضيحية
                showCartItemsMessage($form);
            }
        });
    }

    /**
     * إظهار رسالة توضيحية عند وجود منتجات في السلة
     */
    function showCartItemsMessage($form) {
        // إزالة الرسالة السابقة إن وجدت
        $form.find('.cart-items-message').remove();

        // الحصول على عدد المنتجات في السلة
        const cartCount = parseInt($cartCount.text()) || 0;
        const cartText = cartCount === 1 ? 'منتج واحد' : cartCount + ' منتجات';

        // إضافة رسالة جديدة
        const message = `
            <div class="cart-items-message">
                <div class="cart-message-content">
                    <i class="fas fa-shopping-cart"></i>
                    <p>لديك ${cartText} في السلة بالفعل</p>
                    <small>تم إخفاء حقول المعلومات الشخصية لأنها محفوظة من الطلب السابق.<br>يمكنك إضافة هذا المنتج مباشرة أو مراجعة السلة.</small>
                    <div class="cart-message-actions">
                        <button type="button" class="btn-view-cart">
                            <i class="fas fa-eye"></i> عرض السلة
                        </button>
                    </div>
                </div>
            </div>
        `;

        // إدراج الرسالة في بداية النموذج
        $form.prepend(message);

        // ربط حدث النقر على زر عرض السلة
        $form.find('.btn-view-cart').on('click', function() {
            openCartSidebar();
        });
    }

    // إضافة أنماط CSS للإشعارات والرسائل
    if ($('.cart-notification').length === 0) {
        $('head').append(`
            <style>
            .cart-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10001;
                display: none;
                max-width: 300px;
                word-wrap: break-word;
            }
            .cart-notification.success { background: #28a745; }
            .cart-notification.error { background: #dc3545; }
            .cart-notification.info { background: #17a2b8; }
            body.cart-sidebar-open { overflow: hidden; }

            /* أنماط رسالة السلة */
            .cart-items-message {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 12px;
                margin-bottom: 20px;
                text-align: center;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                animation: slideInDown 0.5s ease-out;
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .cart-message-content i {
                font-size: 2em;
                margin-bottom: 10px;
                display: block;
                animation: bounce 2s infinite;
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }

            .cart-message-content p {
                font-size: 1.1em;
                font-weight: 600;
                margin: 10px 0 5px 0;
            }

            .cart-message-content small {
                opacity: 0.9;
                display: block;
                margin-bottom: 15px;
                line-height: 1.4;
            }

            .cart-message-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .btn-view-cart {
                background: rgba(255,255,255,0.2);
                border: 2px solid rgba(255,255,255,0.3);
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 0.9em;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 8px;
            }

            .btn-view-cart:hover {
                background: rgba(255,255,255,0.3);
                border-color: rgba(255,255,255,0.5);
                transform: translateY(-2px);
                color: white;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }

            /* إخفاء الحقول عند وجود منتجات في السلة */
            .form-elrakami-form.cart-has-items .form-elrakami-fields,
            .form-elrakami-form.cart-has-items .form-elrakami-address-fields,
            .form-elrakami-form.cart-has-items .shipping-methods-container {
                display: none !important;
            }

            /* تحسينات للشاشات الصغيرة */
            @media (max-width: 768px) {
                .cart-items-message {
                    margin: 10px;
                    padding: 15px;
                }

                .btn-view-cart {
                    width: 100%;
                    justify-content: center;
                }
            }
            </style>
        `);
    }
});
