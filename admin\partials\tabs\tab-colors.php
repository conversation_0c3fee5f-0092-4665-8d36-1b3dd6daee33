<?php
/**
 * تبويب الألوان والتصميم
 * 
 * @var array $form_settings إعدادات النموذج
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}
?>
<h3>الألوان الرئيسية</h3>

<div class="settings-group">
    <div class="settings-field">
        <label for="primary-color">اللون الرئيسي</label>
        <input type="text" name="settings[icons_color]" id="primary-color" class="color-picker" 
            value="<?php echo esc_attr($form_settings['icons_color'] ?? '#2563eb'); ?>">
        <p class="description">يتحكم في لون الأيقونات والعناصر التفاعلية وتأثيرات التحويم.</p>
    </div>
    
    <div class="settings-field">
        <label for="card-bg-color">خلفية النموذج</label>
        <input type="text" name="settings[card_bg_color]" id="card-bg-color" class="color-picker" 
            value="<?php echo esc_attr($form_settings['card_bg_color'] ?? '#ffffff'); ?>">
        <p class="description">لون خلفية النموذج بالكامل.</p>
    </div>
</div>

<h3>تصميم النموذج</h3>

<div class="settings-group">
    <div class="settings-field">
        <label for="card-border-radius">حواف النموذج</label>
        <input type="range" name="settings[card_border_radius]" id="card-border-radius" 
            value="<?php echo esc_attr($form_settings['card_border_radius'] ?? '8'); ?>" min="0" max="30" step="1"
            oninput="this.nextElementSibling.value = this.value + ' بكسل'">
        <o><?php echo esc_attr($form_settings['card_border_radius'] ?? '8'); ?> بكسل</o>
    </div>
    
    <div class="settings-field">
        <label for="card-shadow">ظل النموذج</label>
        <select name="settings[card_shadow]" id="card-shadow">
            <option value="none" <?php selected($form_settings['card_shadow'] ?? 'medium', 'none'); ?>>بدون ظل</option>
            <option value="small" <?php selected($form_settings['card_shadow'] ?? 'medium', 'small'); ?>>خفيف</option>
            <option value="medium" <?php selected($form_settings['card_shadow'] ?? 'medium', 'medium'); ?>>متوسط</option>
            <option value="large" <?php selected($form_settings['card_shadow'] ?? 'medium', 'large'); ?>>كثيف</option>
        </select>
    </div>
    
    <div class="settings-field">
        <label for="form-border-style">نوع حدود النموذج</label>
        <select name="settings[form_border_style]" id="form-border-style">
            <option value="none" <?php selected($form_settings['form_border_style'] ?? 'none', 'none'); ?>>بدون حدود</option>
            <option value="solid" <?php selected($form_settings['form_border_style'] ?? 'none', 'solid'); ?>>خط متصل</option>
            <option value="dashed" <?php selected($form_settings['form_border_style'] ?? 'none', 'dashed'); ?>>خط متقطع</option>
            <option value="dotted" <?php selected($form_settings['form_border_style'] ?? 'none', 'dotted'); ?>>نقاط</option>
        </select>
    </div>
    
    <div class="settings-field border-settings" style="<?php echo ($form_settings['form_border_style'] ?? 'none') == 'none' ? 'display: none;' : ''; ?>">
        <label for="form-border-color">لون الحدود</label>
        <input type="text" name="settings[form_border_color]" id="form-border-color" class="color-picker" 
            value="<?php echo esc_attr($form_settings['form_border_color'] ?? '#e2e8f0'); ?>">
    </div>
    
    <div class="settings-field border-settings" style="<?php echo ($form_settings['form_border_style'] ?? 'none') == 'none' ? 'display: none;' : ''; ?>">
        <label for="form-border-width">سمك الحدود</label>
        <input type="range" name="settings[form_border_width]" id="form-border-width" 
            value="<?php echo esc_attr($form_settings['form_border_width'] ?? '1'); ?>" min="1" max="10" step="1"
            oninput="this.nextElementSibling.value = this.value + ' بكسل'">
        <o><?php echo esc_attr($form_settings['form_border_width'] ?? '1'); ?> بكسل</o>
    </div>

    <div class="settings-field">
        <label for="text-color">لون النص</label>
        <input type="text" name="settings[text_color]" id="text-color" class="color-picker" 
            value="<?php echo esc_attr($form_settings['text_color'] ?? '#1e293b'); ?>">
        <p class="description">لون النص الرئيسي في النموذج.</p>
    </div>
</div>