/**
 * Admin CSS for Form Elrakami
 */

/* نمط زر التبديل */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch.small {
    width: 46px;
    height: 26px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    right: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

.toggle-switch.small .slider:before {
    height: 18px;
    width: 18px;
    right: 4px;
    bottom: 4px;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    transform: translateX(-26px);
}

.toggle-switch.small input:checked + .slider:before {
    transform: translateX(-20px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.toggle-label {
    font-weight: 600;
    margin-right: 15px;
    vertical-align: middle;
}

.sticky-bar-toggle {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 5px;
}

.toggle-switch-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

/* إعدادات الشريط المثبت */
.sticky-bar-editor {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sticky-bar-settings {
    margin-top: 20px;
}

.toggle-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

/* معاينة الشريط المثبت */
.sticky-bar-preview-container {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.sticky-bar-preview-container h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
}

.sticky-bar-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.preview-product-info {
    display: flex;
    align-items: center;
}

.preview-product-image {
    width: 50px;
    height: 50px;
    margin-left: 10px;
    border-radius: 5px;
    overflow: hidden;
}

.img-placeholder {
    width: 100%;
    height: 100%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-product-details {
    display: flex;
    flex-direction: column;
}

.preview-product-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.preview-product-price {
    color: #4CAF50;
    font-weight: bold;
}

.sticky-button-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
}

.sticky-icon-preview {
    margin-left: 8px;
}

/* خيارات الشريط المثبت */
.sticky-bar-options {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.sticky-options-col {
    flex: 1;
    min-width: 250px;
    padding: 0 10px;
    margin-bottom: 15px;
}

/* محدد الأيقونات */
.icon-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.icon-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
}

.icon-option input {
    display: none;
}

.icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: #f5f5f5;
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    transition: all 0.2s;
}

.icon-option input:checked + .icon-preview {
    background-color: #e3f2fd;
    border-color: #2196F3;
    color: #2196F3;
}

/* محدد اتجاه التدرج */
.gradient-direction-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.direction-option {
    cursor: pointer;
}

.direction-option input {
    display: none;
}

.direction-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #f5f5f5;
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    transition: all 0.2s;
}

.direction-option input:checked + .direction-preview {
    background-color: #e3f2fd;
    border-color: #2196F3;
    color: #2196F3;
}

/* Form editor styles */
.form-field-row {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    transition: all 0.2s;
    overflow: hidden;
}

.form-field-row:hover {
    border-color: #bbb;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.field-header {
    padding: 8px 10px;
    background-color: #f8f8f8;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.field-header:hover {
    background-color: #f2f2f2;
}

.field-header h3 {
    margin: 0;
    flex-grow: 1;
    font-size: 13px;
    font-weight: 500;
    color: #444;
}

.field-actions {
    display: flex;
    align-items: center;
}

.field-actions span {
    cursor: pointer;
    margin-left: 8px;
    color: #0073aa;
    width: 16px;
    height: 16px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    border-radius: 3px;
}

.field-actions span:hover {
    color: #00a0d2;
    transform: scale(1.15);
    background-color: rgba(0, 115, 170, 0.1);
}

.field-actions .delete-field {
    color: #dc3232;
}

.field-actions .delete-field:hover {
    color: #dc3232;
    background-color: rgba(220, 50, 50, 0.1);
}

.handle {
    cursor: move;
    margin-right: 8px;
    color: #aaa;
    transition: color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.field-header:hover .handle {
    color: #888;
}

/* تنسيقات ترتيب عناصر النموذج */
#form-elements-sortable-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ui-state-highlight {
    height: 60px;
    line-height: 60px;
    border: 2px dashed #2271b1;
    background-color: rgba(34, 113, 177, 0.1);
    border-radius: 6px;
    margin-bottom: 15px;
}

.field-details {
    padding: 15px;
    border-top: 1px solid #f2f2f2;
    background-color: #fcfcfc;
}

.field-details p {
    margin: 0 0 10px 0;
    color: #666;
}

.required-field {
    display: inline-flex;
    align-items: center;
    color: #fff;
    background-color: #d63638;
    font-weight: 500;
    font-size: 10px;
    padding: 1px 6px;
    border-radius: 8px;
}

.optional-field {
    display: inline-flex;
    align-items: center;
    color: #fff;
    background-color: #788;
    font-weight: 500;
    font-size: 10px;
    padding: 1px 6px;
    border-radius: 8px;
}

.visible-field {
    display: inline-flex;
    align-items: center;
    color: #fff;
    background-color: #2271b1;
    font-weight: 500;
    font-size: 10px;
    padding: 1px 6px;
    border-radius: 8px;
}

.hidden-field {
    display: inline-flex;
    align-items: center;
    color: #fff;
    background-color: #777;
    font-weight: 500;
    font-size: 10px;
    padding: 1px 6px;
    border-radius: 8px;
}

.field-buttons {
    margin-top: 12px;
    margin-bottom: 8px;
}

#add-field-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 5px 12px;
    height: auto;
    transition: all 0.2s;
    background-color: #f7f7f7;
    border-color: #ccc;
    font-size: 12px;
}

#add-field-button:hover {
    background-color: #f0f0f0;
    border-color: #999;
}

#add-field-button .dashicons {
    margin-right: 4px;
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Modal styles */
#field-editor-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(2px);
    animation: fadeIn 0.2s ease;
    overflow-y: auto;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 15px;
    border: 1px solid #ddd;
    width: 70%;
    border-radius: 4px;
    max-width: 600px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    animation: slideInUp 0.3s ease;
    position: relative;
}

.modal-close {
    color: #666;
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 22px;
    font-weight: bold;
    transition: all 0.2s;
    line-height: 1;
}

.modal-close:hover,
.modal-close:focus {
    color: #0073aa;
    text-decoration: none;
    cursor: pointer;
    transform: scale(1.1);
}

.field-editor-form h2 {
    font-size: 18px;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    color: #23282d;
}

.field-row {
    margin-bottom: 12px;
    position: relative;
}

.field-row label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #23282d;
    font-size: 13px;
}

.field-row input[type="text"],
.field-row textarea,
.field-row select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: border-color 0.2s, box-shadow 0.2s;
    font-size: 12px;
}

.field-row input[type="text"]:focus,
.field-row textarea:focus,
.field-row select:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.field-row p.description {
    font-size: 11px;
    color: #777;
    margin-top: 3px;
    margin-bottom: 0;
    font-style: italic;
}

.field-checkbox-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 3px 0;
}

.field-checkbox-row input {
    margin-right: 6px;
}

.field-checkbox-row label {
    margin-bottom: 0;
    cursor: pointer;
    font-size: 12px;
}

.field-row button {
    margin-right: 8px;
    padding: 5px 10px;
    height: auto;
    font-size: 12px;
}

.field-row button.button-primary {
    background: #2271b1;
    border-color: #2271b1;
    transition: all 0.2s;
}

.field-row button.button-primary:hover {
    background: #135e96;
    border-color: #135e96;
}

/* Fix for RTL admin in WordPress */
body.rtl .field-actions span {
    margin-left: 0;
    margin-right: 10px;
}

body.rtl .handle {
    margin-right: 0;
    margin-left: 12px;
}

body.rtl .modal-close {
    right: auto;
    left: 20px;
}

body.rtl .field-checkbox-row input {
    margin-right: 0;
    margin-left: 8px;
}

body.rtl #add-field-button .dashicons {
    margin-right: 0;
    margin-left: 5px;
}

body.rtl .field-row button {
    margin-right: 0;
    margin-left: 10px;
}

/* Registrations list styles */
.submission-data table {
    direction: rtl;
    text-align: right;
}

.submission-data th {
    background-color: #f6f7f7;
}

.registrations-table .column-data {
    width: 40%;
}

/* Settings page styles */
#form-elrakami-settings .form-table th {
    width: 200px;
}

/* Color picker fix for RTL */
body.rtl .wp-picker-container .wp-color-result {
    margin: 0 0 3px 5px;
}

/* RTL support for all admin pages */
.rtl h1.wp-heading-inline + .page-title-action {
    margin-right: 4px;
    margin-left: 0;
}

.rtl .form-elrakami-info-box {
    text-align: right;
}

.rtl .form-elrakami-data th,
.rtl .form-elrakami-data td {
    text-align: right;
}

/* Delete confirmation style */
.submitdelete {
    color: #d63638;
}

.submitdelete:hover {
    color: #b32d2e;
}

/* إعدادات التصميم المُبوّبة */
.design-settings-container {
    padding: 10px 5px;
}

.design-settings-tabs {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.design-settings-tabs .tabs-nav {
    display: flex;
    flex-direction: column;
    list-style: none;
    padding: 0;
    margin: 0;
    border-right: 1px solid #ccc;
    min-width: 160px;
    flex-shrink: 0;
}

.design-settings-tabs .tabs-nav li {
    margin: 0;
    padding: 0;
}

.design-settings-tabs .tabs-nav a {
    display: block;
    padding: 8px 10px;
    text-decoration: none;
    color: #555;
    border: 1px solid transparent;
    border-right: none;
    margin-right: -1px;
    transition: all 0.2s ease;
    border-radius: 3px 0 0 3px;
    font-size: 12px;
}

.design-settings-tabs .tabs-nav li.active a {
    background: #f7f7f7;
    border-color: #ccc;
    border-right-color: #f7f7f7;
    color: #2271b1;
    font-weight: 500;
}

.design-settings-tabs .tabs-nav a:hover:not(.active) {
    background-color: #f0f0f0;
    color: #2271b1;
}

.design-settings-tabs .tab-content {
    padding: 12px;
    background: #f7f7f7;
    border: 1px solid #ccc;
    border-right: 1px solid #ccc;
    flex-grow: 1;
}

.design-settings-tabs h3 {
    margin-top: 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ddd;
    margin-bottom: 10px;
    font-size: 14px;
}

.settings-group {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.settings-field {
    margin-bottom: 10px;
}

.settings-field label {
    display: block;
    margin-bottom: 3px;
    font-weight: 500;
    font-size: 12px;
}

.settings-field input[type="range"] {
    width: 100%;
    max-width: 180px;
    margin-left: 0;
    margin-right: 8px;
    vertical-align: middle;
    height: 20px;
}

.settings-field output {
    display: inline-block;
    min-width: 40px;
    vertical-align: middle;
    font-size: 12px;
}

.settings-field .description {
    font-size: 11px;
    font-style: italic;
    color: #666;
    margin-top: 3px;
    margin-bottom: 0;
}

/* تحسينات منتقي الألوان */
.wp-picker-container {
    position: relative;
    display: inline-block;
}

.rtl .design-settings-tabs .tabs-nav {
    padding-right: 0;
    border-right: none;
    border-left: 1px solid #ccc;
}

.rtl .design-settings-tabs .tabs-nav a {
    border-right: 1px solid transparent;
    border-left: none;
    margin-right: 0;
    margin-left: -1px;
    border-radius: 0 4px 4px 0;
}

.rtl .design-settings-tabs .tabs-nav li.active a {
    border-right-color: #ccc;
    border-left-color: #f7f7f7;
}

.rtl .design-settings-tabs .tab-content {
    border-right: none;
    border-left: 1px solid #ccc;
}

.rtl .settings-field input[type="range"] {
    margin-right: 0;
    margin-left: 10px;
}

/* تحسينات لحقول الإدخال */
.design-settings-tabs input[type="text"],
.design-settings-tabs input[type="number"],
.design-settings-tabs select {
    width: 100%;
    max-width: 100%;
    padding: 4px 8px;
    height: 28px;
    font-size: 12px;
}

/* تأثيرات بصرية لزر التبديل */
.toggle-field {
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-field:hover {
    transform: scale(1.2);
}

.toggle-field.dashicons-visibility {
    color: #2271b1;
}

.toggle-field.dashicons-hidden {
    color: #d63638;
}

.field-toggled {
    animation: field-toggle-flash 0.5s;
}

@keyframes field-toggle-flash {
    0% { background-color: transparent; }
    50% { background-color: rgba(35, 113, 177, 0.1); }
    100% { background-color: transparent; }
}