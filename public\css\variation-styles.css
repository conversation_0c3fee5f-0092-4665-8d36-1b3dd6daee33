/**
 * أنماط CSS لمتغيرات المنتج
 */

/* حاوية المتغيرات */
.form-elrakami-variations {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: var(--form-input-radius);
    border: 1px solid var(--form-border-color);
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* عنوان قسم المتغيرات */
.variations-title {
    font-size: 1rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    color: var(--form-text-color);
    text-align: center;
}

/* حاوية السمات */
.variation-attribute {
    margin-bottom: 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

/* عنوان السمة */
.variation-attribute-label {
    font-weight: 500;
    margin-bottom: 0.8rem;
    font-size: 0.95rem;
    color: var(--form-label-color);
}

/* حاوية أزرار المتغيرات */
.variation-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    box-sizing: border-box;
}

/* حاوية السمات المتغيرة */
.variations-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    box-sizing: border-box;
}

/* تحسين عرض المتغيرات في نمط العرض الموسع */
.variation-display-extended .variation-buttons-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
}

/* نمط العرض الممتد */
.variation-display-extended .variation-attribute {
    margin-bottom: 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

.variation-display-extended .variation-buttons-container {
    flex-direction: column;
    gap: 12px;
    width: 100%;
    box-sizing: border-box;
}

.variation-display-extended .variation-button-label {
    width: 100%;
    margin: 0 0 12px 0;
    box-sizing: border-box;
    display: block;
}

/* أنماط المتغيرات الممتدة (Compact Swatch) */
.variation-display-extended .compact-swatch-option {
    display: flex;
    align-items: center;
    border-radius: 12px;
    background: #fff;
    border: 2px solid #f0f0f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 12px;
    min-height: 80px;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    position: relative;
    transition: border-color 0.3s, box-shadow 0.3s;
    box-sizing: border-box;
}

.variation-display-extended .compact-swatch-option:hover {
    border-color: var(--form-primary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.variation-display-extended .variation-button-input:checked + .compact-swatch-option {
    border-color: var(--form-primary-color);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.variation-display-extended .variation-button-input:checked + .compact-swatch-option::after {
    content: '✔';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    background-color: var(--form-primary-color);
    color: white;
    font-size: 14px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    border-radius: 50%;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.variation-display-extended .variant-image {
    width: 55px;
    height: 55px;
    flex-shrink: 0;
    margin-left: 12px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
}

.variation-display-extended .variant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.variation-display-extended .variant-details {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    min-width: 0;
}

.variation-display-extended .variant-info {
    flex-grow: 1;
    min-width: 0;
}

.variation-display-extended .variant-title {
    font-weight: bold;
    font-size: 15px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.variation-display-extended .variant-description {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.variation-display-extended .variant-price-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 12px;
    flex-shrink: 0;
}

.variation-display-extended .variant-price {
    color: var(--form-primary-color);
    font-weight: bold;
    font-size: 15px;
}

.variation-display-extended .woocommerce-Price-currencySymbol {
    font-size: 12px;
}

.variation-display-extended .per-piece {
    font-size: 11px;
    color: #777;
    margin-right: 3px;
}

.variation-display-extended .variant-regular-price {
    font-size: 11px;
    color: #bbb;
    text-decoration: line-through;
    font-weight: 300;
    opacity: 0.8;
}

/* تنسيق السعر الافتراضي داخل بطاقة المتغير */
.variation-display-extended .variant-regular-price .woocommerce-Price-amount,
.variation-display-extended del .woocommerce-Price-amount {
    font-size: 11px;
    color: #bbb;
    font-weight: 300;
    opacity: 0.8;
}

/* تنسيق رمز العملة داخل السعر الافتراضي */
.variation-display-extended .variant-regular-price .woocommerce-Price-currencySymbol,
.variation-display-extended del .woocommerce-Price-currencySymbol {
    font-size: 10px;
    color: #bbb;
    font-weight: 300;
}

.variation-display-extended .discount-badge {
    position: absolute;
    bottom: 0;
    left: 0;
    background: var(--form-primary-color);
    color: white;
    font-size: 12px;
    font-weight: bold;
    padding: 4px 10px;
    border-radius: 0 8px 0 0;
    z-index: 1;
    box-shadow: 0 -2px 6px rgba(0,0,0,0.2);
    line-height: 1.2;
    transform-origin: bottom left;
    transition: transform 0.3s ease;
}

.variation-display-extended .compact-swatch-option:hover .discount-badge {
    transform: scale(1.05);
}

/* تحسين مظهر شارة الخصم عند تحديد المتغير */
.variation-display-extended .variation-button-input:checked + .compact-swatch-option .discount-badge {
    background: #ff4757;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.3);
}

/* زر المتغير */
.variation-button-label {
    display: inline-block;
    margin-left: 8px;
    margin-bottom: 8px;
    cursor: pointer;
}

.variation-button-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.variation-button {
    display: inline-block;
    padding: 8px 12px;
    background-color: #fff;
    border: 1px solid var(--form-border-color);
    border-radius: var(--form-input-radius);
    font-size: 0.9rem;
    text-align: center;
    transition: all 0.2s ease;
}

/* حالة التحويم */
.variation-button-label:hover .variation-button {
    border-color: var(--form-primary-color);
}

/* حالة التحديد */
.variation-button-input:checked + .variation-button {
    background-color: var(--form-primary-color);
    color: white;
    border-color: var(--form-primary-color);
}

/* معلومات السعر والتوفر */
.variation-price-info {
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.variation-price {
    font-weight: 600;
    color: var(--form-primary-color);
}

.variation-availability {
    font-size: 0.85rem;
}

.variation-availability.in-stock {
    color: #10b981;
}

.variation-availability.out-of-stock {
    color: #ef4444;
}

/* أنماط خاصة للمتغيرات ذات الصور */
.variation-button-label.has-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin: 0 8px 12px 0;
}

.variation-button-label.has-image .variation-button {
    width: 50px;
    height: 50px;
    padding: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    margin-bottom: 5px;
}

.variation-button-label.has-image .variation-name {
    font-size: 10px;
    color: #333;
    max-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* أنماط خاصة للألوان */
.variation-type-color .variation-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    padding: 0;
    text-align: center;
    line-height: 40px;
}

/* أنماط خاصة لدوائر الألوان */
.variation-button-label.color-circle {
    margin: 0 8px 8px 0;
}

.variation-button.color-circle-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    padding: 0;
    position: relative;
    border: 2px solid transparent;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.variation-button.color-circle-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.variation-button-input:checked + .variation-button.color-circle-button {
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px var(--form-primary-color), 0 4px 8px rgba(0,0,0,0.2);
}

.variation-button.color-circle-button .color-name {
    font-size: 10px;
    white-space: nowrap;
    text-shadow: 0 0 3px rgba(0,0,0,0.7);
    font-weight: bold;
}

/* تحسين عرض دوائر الألوان */
.variation-display-circle .variation-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    margin: 10px 0;
}

.variation-button-label.color-circle {
    margin: 0;
    position: relative;
}

/* إضافة تأثير الظل للدوائر */
.variation-button.color-circle-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    box-shadow: inset 0 -2px 5px rgba(0,0,0,0.2), inset 0 2px 5px rgba(255,255,255,0.5);
    pointer-events: none;
}

/* تأثير التحديد للدوائر */
.variation-button-label.color-circle.selected .variation-button.color-circle-button {
    transform: scale(1.1);
}

/* تنسيق حاوية أزرار المتغيرات حسب نوع العرض */
.variation-display-square .variation-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    flex-direction: row;
}

.variation-display-circle .variation-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    flex-direction: row;
    justify-content: center;
}

/* أنماط خاصة للمقاسات */
.variation-type-size .variation-button {
    min-width: 40px;
    text-align: center;
    font-weight: 500;
}

/* أنماط القائمة المنسدلة */
.variation-dropdown {
    width: 100%;
    padding: 10px 12px;
    border-radius: var(--form-input-radius);
    border: 1px solid var(--form-border-color);
    background-color: #fff;
    font-size: 0.95rem;
    color: var(--form-text-color);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>');
    background-repeat: no-repeat;
    background-position: left 12px center;
    padding-left: 35px;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.variation-dropdown:hover, .variation-dropdown:focus {
    border-color: var(--form-primary-color);
    outline: none;
}

.variation-dropdown option {
    padding: 8px;
}

/* أنماط الأزرار النصية */
.variation-button-label.text-button {
    margin: 0 5px 5px 0;
}

.variation-button.text-only-button {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 2px solid var(--form-border-color);
    background-color: #fff;
    color: var(--form-text-color);
}

.variation-button-label:hover .variation-button.text-only-button {
    border-color: var(--form-primary-color);
    color: var(--form-primary-color);
}

.variation-button-input:checked + .variation-button.text-only-button {
    background-color: var(--form-primary-color);
    color: white;
    border-color: var(--form-primary-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* أنماط البطاقات الموسعة مع الاسم كاملاً */
.variation-button-label.extended-button {
    width: 100%;
    margin: 0 0 10px 0;
    display: block;
}

.variation-button-label.extended-button .compact-swatch-option {
    display: flex;
    align-items: center;
    border-radius: 12px;
    background: #fff;
    border: 2px solid #f0f0f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 12px 15px;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    position: relative;
    transition: border-color 0.3s, box-shadow 0.3s;
    box-sizing: border-box;
}

.variation-button-label.extended-button:hover .compact-swatch-option {
    border-color: var(--form-primary-color);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.variation-button-input:checked + .compact-swatch-option {
    border-color: var(--form-primary-color);
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

.variation-button-input:checked + .compact-swatch-option::after {
    content: '✓';
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    background-color: var(--form-primary-color);
    color: white;
    font-size: 14px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    border-radius: 50%;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* تحسين عرض صورة المتغير في البطاقات الموسعة */
.variation-button-label.extended-button .variant-image {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
    margin-left: 15px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.variation-button-label.extended-button .variant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* تحسين عرض تفاصيل المتغير في البطاقات الموسعة */
.variation-button-label.extended-button .variant-details {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    min-width: 0;
    width: calc(100% - 80px); /* تحديد عرض التفاصيل بناءً على عرض الصورة والهوامش */
}

.variation-button-label.extended-button .variant-info {
    flex-grow: 1;
    min-width: 0;
    padding-right: 10px;
}

.variation-button-label.extended-button .variant-title {
    font-weight: bold;
    font-size: 15px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.variation-button-label.extended-button .variant-description {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* تحسين عرض السعر في البطاقات الموسعة */
.variation-button-label.extended-button .variant-price-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-left: 12px;
    flex-shrink: 0;
    min-width: 80px;
    text-align: left;
}

.variation-button-label.extended-button .variant-price {
    color: var(--form-primary-color);
    font-weight: bold;
    font-size: 15px;
    white-space: nowrap;
}

.variation-button-label.extended-button .variant-regular-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
    font-weight: 300;
    opacity: 0.8;
    white-space: nowrap;
}

/* تحسين عرض شارة الخصم */
.variation-button-label.extended-button .discount-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: var(--form-primary-color);
    color: white;
    font-size: 11px;
    font-weight: bold;
    padding: 3px 8px;
    border-radius: 0 0 8px 0;
    z-index: 1;
}

/* تحسين عرض البطاقات الموسعة على الشاشات الصغيرة */
@media (max-width: 576px) {
    .variation-button-label.extended-button .compact-swatch-option {
        padding: 10px;
    }

    .variation-button-label.extended-button .variant-image {
        width: 50px;
        height: 50px;
        margin-left: 10px;
    }

    .variation-button-label.extended-button .variant-details {
        width: calc(100% - 65px);
    }

    .variation-button-label.extended-button .variant-price-container {
        min-width: 70px;
    }

    .variation-button-input:checked + .compact-swatch-option::after {
        right: 10px;
        width: 20px;
        height: 20px;
        font-size: 12px;
    }
}

/* ===== بطاقات العروض المستطيلة ===== */
.variation-display-offer_cards_rectangle .variation-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

.variation-display-offer_cards_rectangle .variation-button-label {
    width: 100%;
    margin: 0;
    position: relative;
    box-sizing: border-box;
}

.variation-display-offer_cards_rectangle .offer-card {
    display: flex;
    align-items: center;
    border-radius: 8px;
    background: #fff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 15px;
    width: 100%;
    max-width: 100%;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
    box-sizing: border-box;
}

.variation-display-offer_cards_rectangle .offer-card:hover {
    border-color: var(--form-primary-color);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.variation-display-offer_cards_rectangle .variation-button-input:checked + .offer-card {
    border-color: var(--form-primary-color);
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    transform: translateY(-3px);
}

.variation-display-offer_cards_rectangle .offer-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 5px;
    background: linear-gradient(to bottom, #f97316, #f43f5e);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.variation-display-offer_cards_rectangle .variation-button-input:checked + .offer-card::before {
    opacity: 1;
}

.variation-display-offer_cards_rectangle .offer-image {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
    margin-left: 15px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    background-color: #f9f9f9;
}

.variation-display-offer_cards_rectangle .offer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.variation-display-offer_cards_rectangle .offer-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 80px);
    box-sizing: border-box;
    overflow: hidden;
}

.variation-display-offer_cards_rectangle .offer-title {
    font-weight: 700;
    font-size: 16px;
    color: #1e293b;
    margin-bottom: 5px;
    white-space: normal;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.variation-display-offer_cards_rectangle .offer-description {
    font-size: 13px;
    color: #64748b;
    margin-bottom: 8px;
    white-space: normal;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.variation-display-offer_cards_rectangle .offer-footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end; /* تغيير من space-between إلى flex-end لجعل السعر في أقصى اليسار */
    align-items: center;
    margin-top: 5px;
    width: 100%;
    box-sizing: border-box;
    padding: 5px 0;
}

/* عنصر الكمية في بطاقات العروض */
.variation-display-offer_cards_rectangle .offer-quantity {
    display: none; /* إخفاء عنصر الكمية الحالي */
}

/* شارة الكمية في الزاوية العليا اليمنى */
.variation-display-offer_cards_rectangle .quantity-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--form-primary-color);
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 0 0 0 8px;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    direction: ltr;
}

.variation-display-offer_cards_rectangle .offer-price {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: 80px;
    text-align: left;
    margin-left: 0; /* إزالة الهامش الأيسر */
    flex-shrink: 0;
    order: 2; /* جعل السعر يظهر في النهاية (أقصى اليسار) */
}

.variation-display-offer_cards_rectangle .price-current {
    font-weight: 700;
    font-size: 16px;
    color: var(--form-primary-color);
    white-space: nowrap;
    display: flex;
    align-items: center;
    margin-right: 5px;
}

.variation-display-offer_cards_rectangle .price-original {
    font-size: 13px;
    color: #94a3b8;
    text-decoration: line-through;
    white-space: nowrap;
    margin-right: 8px;
}

.variation-display-offer_cards_rectangle .per-piece {
    font-size: 10px;
    white-space: nowrap;
    color: #a0aec0;
    margin-right: 4px;
    font-weight: normal;
    opacity: 0.8;
}

.variation-display-offer_cards_rectangle .discount-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: #ef4444;
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 4px 10px;
    border-radius: 0 0 8px 0;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.variation-display-offer_cards_rectangle .offer-special-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: #f97316;
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 4px 10px;
    border-radius: 0 0 8px 0;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* ===== بطاقات العروض المربعة ===== */
.variation-display-offer_cards_square .variation-buttons-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
    width: 100%;
}

.variation-display-offer_cards_square .variation-button-label {
    margin: 0;
    position: relative;
}

.variation-display-offer_cards_square .offer-card {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    background: #fff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 0;
    width: 100%;
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.variation-display-offer_cards_square .offer-card:hover {
    border-color: var(--form-primary-color);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.variation-display-offer_cards_square .variation-button-input:checked + .offer-card {
    border-color: var(--form-primary-color);
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    transform: translateY(-3px);
}

.variation-display-offer_cards_square .offer-image {
    width: 100%;
    height: 120px;
    overflow: hidden;
    position: relative;
}

.variation-display-offer_cards_square .offer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.variation-display-offer_cards_square .offer-content {
    padding: 15px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.variation-display-offer_cards_square .offer-title {
    font-weight: 700;
    font-size: 16px;
    color: #1e293b;
    margin-bottom: 5px;
    text-align: center;
}

.variation-display-offer_cards_square .offer-description {
    font-size: 13px;
    color: #64748b;
    margin-bottom: 10px;
    text-align: center;
    flex-grow: 1;
}

.variation-display-offer_cards_square .offer-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    border-top: 1px solid #f1f5f9;
    padding-top: 8px;
    padding-bottom: 5px;
}

/* عنصر الكمية في بطاقات العروض المربعة */
.variation-display-offer_cards_square .offer-quantity {
    display: none; /* إخفاء عنصر الكمية الحالي */
}

/* شارة الكمية في الزاوية العليا اليمنى للبطاقات المربعة */
.variation-display-offer_cards_square .quantity-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--form-primary-color);
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 4px;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    direction: ltr;
}

.variation-display-offer_cards_square .offer-price {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start; /* تغيير من flex-end إلى flex-start لجعل السعر في أقصى اليسار */
    width: 100%; /* تأكيد على استخدام العرض الكامل */
}

.variation-display-offer_cards_square .price-current {
    font-weight: 700;
    font-size: 16px;
    color: var(--form-primary-color);
    white-space: nowrap;
    display: flex;
    align-items: center;
    margin-right: 5px;
}

.variation-display-offer_cards_square .price-original {
    font-size: 13px;
    color: #94a3b8;
    text-decoration: line-through;
    white-space: nowrap;
    margin-right: 8px;
}

.variation-display-offer_cards_square .per-piece {
    font-size: 10px;
    white-space: nowrap;
    color: #a0aec0;
    margin-right: 4px;
    font-weight: normal;
    opacity: 0.8;
}

.variation-display-offer_cards_square .discount-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ef4444;
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 4px 10px;
    border-radius: 4px;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.variation-display-offer_cards_square .offer-special-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #f97316;
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 4px 10px;
    border-radius: 4px;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 576px) {
    /* تحسين عرض بطاقات المتغير المربعة على الهواتف */
    .variation-display-offer_cards_square .variation-buttons-container {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)); /* تقليل الحجم الأدنى للبطاقات */
        gap: 8px; /* تقليل المسافة بين البطاقات */
    }

    /* تحسين تنسيق بطاقات المتغير المستطيلة على الهواتف */
    .variation-display-offer_cards_rectangle .offer-card {
        padding: 8px; /* تقليل الهوامش الداخلية */
        border-radius: 6px; /* تقليل نصف قطر الزوايا */
    }

    /* تحسين حجم الصور في البطاقات */
    .variation-display-offer_cards_rectangle .offer-image,
    .variation-display-offer_cards_square .offer-image {
        width: 40px; /* تقليل حجم الصورة */
        height: 40px;
        margin-left: 8px;
    }

    /* تحسين عرض المحتوى في البطاقات المستطيلة */
    .variation-display-offer_cards_rectangle .offer-content {
        max-width: calc(100% - 50px); /* تعديل العرض الأقصى بناءً على حجم الصورة الجديد */
    }

    /* تحسين حجم العناوين في البطاقات */
    .variation-display-offer_cards_rectangle .offer-title,
    .variation-display-offer_cards_square .offer-title {
        font-size: 13px; /* تقليل حجم الخط */
        -webkit-line-clamp: 1;
        margin-bottom: 2px; /* تقليل الهامش السفلي */
    }

    /* تحسين حجم الوصف في البطاقات */
    .variation-display-offer_cards_rectangle .offer-description,
    .variation-display-offer_cards_square .offer-description {
        font-size: 11px; /* تقليل حجم الخط */
        -webkit-line-clamp: 2;
        margin-bottom: 2px; /* تقليل الهامش السفلي */
    }

    /* تحسين حجم السعر في البطاقات */
    .variation-display-offer_cards_rectangle .price-current,
    .variation-display-offer_cards_square .price-current {
        font-size: 14px; /* تقليل حجم الخط */
    }

    /* تحسين حجم السعر الأصلي في البطاقات */
    .variation-display-offer_cards_rectangle .price-original,
    .variation-display-offer_cards_square .price-original {
        font-size: 11px; /* تقليل حجم الخط */
    }
}

/* تحسينات إضافية للشاشات الصغيرة جدًا (الهواتف الصغيرة) */
@media (max-width: 380px) {
    /* تحسين عرض بطاقات المتغير المربعة على الهواتف الصغيرة */
    .variation-display-offer_cards_square .variation-buttons-container {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); /* تقليل الحجم الأدنى للبطاقات */
        gap: 6px; /* تقليل المسافة بين البطاقات */
    }

    /* تحسين تنسيق بطاقات المتغير المستطيلة على الهواتف الصغيرة */
    .variation-display-offer_cards_rectangle .offer-card {
        padding: 6px; /* تقليل الهوامش الداخلية */
    }

    /* تحسين حجم الصور في البطاقات */
    .variation-display-offer_cards_rectangle .offer-image,
    .variation-display-offer_cards_square .offer-image {
        width: 35px; /* تقليل حجم الصورة */
        height: 35px;
        margin-left: 6px;
    }

    /* تحسين عرض المحتوى في البطاقات المستطيلة */
    .variation-display-offer_cards_rectangle .offer-content {
        max-width: calc(100% - 45px); /* تعديل العرض الأقصى بناءً على حجم الصورة الجديد */
    }

    /* تحسين حجم العناوين في البطاقات */
    .variation-display-offer_cards_rectangle .offer-title,
    .variation-display-offer_cards_square .offer-title {
        font-size: 12px; /* تقليل حجم الخط */
    }

    /* تحسين حجم الوصف في البطاقات */
    .variation-display-offer_cards_rectangle .offer-description,
    .variation-display-offer_cards_square .offer-description {
        font-size: 10px; /* تقليل حجم الخط */
    }
}

    .variation-display-offer_cards_rectangle .offer-description,
    .variation-display-offer_cards_square .offer-description {
        font-size: 12px;
        -webkit-line-clamp: 1;
        margin-bottom: 5px;
    }

    .variation-display-offer_cards_rectangle .offer-price {
        min-width: 70px;
        margin-left: 2px;
    }

    .variation-display-offer_cards_rectangle .price-current {
        font-size: 14px;
    }

    .variation-display-offer_cards_rectangle .price-original {
        font-size: 12px;
    }

    .variation-display-offer_cards_rectangle .per-piece {
        font-size: 9px;
        margin-right: 2px;
    }

    .variation-display-offer_cards_rectangle .offer-quantity,
    .variation-display-offer_cards_square .offer-quantity {
        font-size: 12px;
    }

    .variation-display-offer_cards_rectangle .offer-quantity-value,
    .variation-display-offer_cards_square .offer-quantity-value {
        font-size: 11px;
        padding: 1px 4px;
    }

    .variation-display-offer_cards_rectangle .discount-badge,
    .variation-display-offer_cards_square .discount-badge {
        font-size: 10px;
        padding: 2px 6px;
    }
}
