# 🚀 دليل البدء السريع - رسوم التوصيل الإضافية

## 📋 خطوات سريعة للبدء

### 1. للمنتجات البسيطة:
1. **اذهب إلى**: المنتجات > تحرير منتج (أو إضافة جديد)
2. **انقر على تبويب**: "الشحن"
3. **ابحث عن القسم**: "رسوم التوصيل الإضافية - Form Elrakami"
4. **أدخل المبلغ**: مثل 200 (بالدينار الجزائري)
5. **أدخل الوصف**: مثل "رسوم إضافية للمنتجات الثقيلة"
6. **احفظ المنتج**: اضغط "تحديث" أو "نشر"

### 2. للمنتجات المتغيرة:
1. **اذهب إلى**: المنتجات > تحرير منتج متغير
2. **انقر على تبويب**: "المتغيرات"
3. **افتح أي متغير**: اضغط على السهم لتوسيع المتغير
4. **ابحث عن القسم**: "رسوم التوصيل الإضافية - Form Elrakami"
5. **أدخل الرسوم**: لكل متغير منفصل
6. **احفظ التغييرات**: اضغط "حفظ التغييرات"

## 🎯 أمثلة عملية

### مثال 1: منتج ثقيل
- **المنتج**: ثلاجة كبيرة
- **الرسوم الإضافية**: 300 دج
- **الوصف**: "رسوم إضافية للأجهزة الكبيرة"
- **النتيجة**: إذا كانت تكلفة التوصيل 500 دج، ستصبح 800 دج

### مثال 2: منتج هش
- **المنتج**: مرآة زجاجية
- **الرسوم الإضافية**: 150 دج
- **الوصف**: "رسوم تغليف خاص للمنتجات الهشة"
- **النتيجة**: إذا كانت تكلفة التوصيل 400 دج، ستصبح 550 دج

### مثال 3: متغيرات مختلفة
- **المنتج**: طاولة خشبية
- **المتغير الصغير**: 0 دج (بدون رسوم)
- **المتغير المتوسط**: 100 دج
- **المتغير الكبير**: 200 دج

## 🔍 كيفية التحقق من النتائج

### 1. في قائمة المنتجات:
- **اذهب إلى**: المنتجات > جميع المنتجات
- **ابحث عن العمود**: "رسوم التوصيل الإضافية"
- **ستجد**: المنتجات التي لها رسوم إضافية مع المبلغ والوصف

### 2. في النموذج للعميل:
- **افتح النموذج**: في الموقع الأمامي
- **اختر منتج**: له رسوم إضافية
- **ستجد**: التكلفة النهائية = التكلفة الأساسية + الرسوم الإضافية

### 3. اختبار سريع:
- **افتح الملف**: `test-shipping-costs-display.php` في المتصفح
- **ستجد**: جدول بجميع المنتجات ورسومها الإضافية

## 🛠️ استكشاف الأخطاء

### المشكلة: لا أجد القسم في صفحة تحرير المنتج
**الحل**:
1. تأكد من تفعيل الإضافة
2. تأكد من وجود WooCommerce
3. جرب تحديث الصفحة
4. تحقق من أنك في تبويب "الشحن" للمنتجات البسيطة

### المشكلة: لا أجد الحقول في المتغيرات
**الحل**:
1. تأكد من أن المنتج من نوع "متغير"
2. افتح المتغير بالضغط على السهم
3. ابحث عن القسم تحت حقل "إظهار شارة الكمية"
4. تأكد من حفظ المنتج الأساسي أولاً

### المشكلة: الرسوم لا تظهر في النموذج
**الحل**:
1. تأكد من حفظ المنتج بعد إضافة الرسوم
2. تحقق من أن المبلغ أكبر من 0
3. امسح الكاش إذا كنت تستخدم إضافات تخزين مؤقت
4. تحقق من سجلات الأخطاء في WordPress

## 📊 نصائح للاستخدام الأمثل

### 1. تنظيم الرسوم:
- **استخدم معايير واضحة**: الوزن، الحجم، الهشاشة
- **كن متسقاً**: نفس الرسوم للمنتجات المشابهة
- **استخدم أوصاف واضحة**: ليفهم العميل سبب الرسوم

### 2. إدارة المخزون:
- **راجع الرسوم بانتظام**: حسب تغيرات التكاليف
- **استخدم العمود في قائمة المنتجات**: للمراجعة السريعة
- **اختبر النموذج**: بعد كل تغيير

### 3. تجربة العميل:
- **كن شفافاً**: اذكر سبب الرسوم الإضافية
- **استخدم أوصاف مفهومة**: تجنب المصطلحات التقنية
- **اختبر من منظور العميل**: تأكد من وضوح التكلفة النهائية

## 🔗 روابط مفيدة

- **إدارة المنتجات**: لوحة التحكم > المنتجات > جميع المنتجات
- **إضافة منتج جديد**: لوحة التحكم > المنتجات > إضافة جديد
- **إدارة مناطق الشحن**: لوحة التحكم > Form Elrakami > إدارة مناطق الشحن
- **اختبار النظام**: افتح ملف `test-shipping-costs-display.php`

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. **راجع هذا الدليل** أولاً
2. **اختبر باستخدام** `test-shipping-costs-display.php`
3. **تحقق من سجلات الأخطاء** في WordPress
4. **تواصل مع فريق الدعم** مع تفاصيل المشكلة

---

**ملاحظة**: هذه الميزة تعمل مع جميع إصدارات WooCommerce المدعومة وتتكامل بسلاسة مع باقي ميزات Form Elrakami.
