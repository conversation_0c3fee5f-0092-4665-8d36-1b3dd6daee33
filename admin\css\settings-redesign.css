/**
 * إعادة تصميم واجهة إعدادات النموذج
 * تحسينات لواجهة المستخدم في صفحة إعدادات النموذج
 */

/* ======= إصلاح أيقونة الإضافة في الشريط الجانبي ======= */
/* التأكد من عدم تأثر أيقونة Form Elrakami بتنسيقات هذه الصفحة */
#adminmenu .toplevel_page_form-elrakami img {
    width: 20px !important;
    height: 20px !important;
    padding-top: 7px !important;
    opacity: 1 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    transform: none !important;
    max-width: 20px !important;
    max-height: 20px !important;
    object-fit: contain !important;
    margin: 0 !important;
    vertical-align: middle !important;
    position: static !important;
    display: inline-block !important;
    float: none !important;
    clear: none !important;
}

/* إصلاح إضافي لضمان عدم تأثر الأيقونة في جميع صفحات الإضافة */
body[class*="form-elrakami"] #adminmenu .toplevel_page_form-elrakami img,
body.admin_page_form-elrakami #adminmenu .toplevel_page_form-elrakami img {
    width: 20px !important;
    height: 20px !important;
    padding-top: 7px !important;
    opacity: 1 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    transform: none !important;
    max-width: 20px !important;
    max-height: 20px !important;
    object-fit: contain !important;
    margin: 0 !important;
    vertical-align: middle !important;
    position: static !important;
    display: inline-block !important;
    float: none !important;
    clear: none !important;
}

/* ======= بطاقة الشعار ======= */
.logo-card {
    text-align: center;
    padding: 20px;
    margin-bottom: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e2e4e7;
    transition: all 0.3s ease;
}

.logo-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.logo-card img {
    max-width: 200px;
    height: auto;
    margin: 0 auto 15px;
    display: block;
}

.logo-card h3 {
    margin: 10px 0;
    color: #3730a3;
    font-size: 18px;
    font-weight: 600;
}

.logo-card p {
    color: #64748b;
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* ======= التنظيم والتسلسل الهرمي ======= */

/* حاوية الإعدادات الرئيسية */
.form-settings-container {
    display: flex;
    gap: 30px;
    margin: 20px 0;
    padding: 20px;
    background: #f0f0f1;
    border-radius: 10px;
    position: relative;
}

/* القائمة الجانبية المحسنة */
.settings-sidebar {
    flex: 0 0 250px;
    background: #fff;
    border-radius: 8px;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 32px;
    height: calc(100vh - 100px);
    border: 1px solid #e2e4e7;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* تقسيم القائمة الجانبية إلى مجموعات */
.settings-nav-group {
    margin-bottom: 24px;
}

.settings-nav-group:last-child {
    border-bottom: none;
}

.settings-nav-group-title {
    padding: 10px 20px;
    margin: 0;
    font-size: 12px;
    font-weight: 600;
    color: #646970;
    text-transform: uppercase;
}

/* قائمة التنقل */
.settings-navigation {
    margin: 0;
    padding: 0;
    list-style: none;
}

.settings-navigation li {
    margin: 0;
    padding: 0;
}

.settings-navigation a {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: #1d2327;
    text-decoration: none;
    border-right: 4px solid transparent;
    transition: all 0.3s ease;
    font-size: 14px;
}

.settings-navigation a .nav-icon {
    margin-left: 8px;
    width: 20px;
    height: 20px;
    color: inherit;
}

.settings-navigation a:hover {
    background: #f6f7f7;
    border-right-color: #cdcdcd;
}

.settings-navigation a.active {
    background: #e0e7ff;
    border-right-color: #3730a3;
    color: #3730a3;
    font-weight: 600;
}

.settings-navigation a.active .nav-icon {
    color: #3730a3;
}

/* زر حفظ الإعدادات */
.save-settings-container {
    position: sticky;
    bottom: 0;
    background: #fff;
    padding: 15px;
    border-top: 1px solid #e2e4e7;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.05);
    margin-top: auto;
}

.save-form-settings {
    width: 100%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 20px !important;
    background-color: #3730a3 !important;
    border-color: #312e81 !important;
    font-size: 14px;
    font-weight: 500;
}

.save-form-settings:hover {
    background-color: #312e81 !important;
}

.save-form-settings .dashicons {
    margin-left: 5px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* ======= تحسين العناصر البصرية ======= */

/* محتوى الإعدادات */
.settings-content {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e2e4e7;
}

/* أقسام الإعدادات */
.settings-section {
    margin-bottom: 40px;
    padding-bottom: 40px;
    border-bottom: 1px solid #eee;
    position: relative;
    scroll-margin-top: 60px; /* للتمرير السلس */
}

.settings-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* إضافة فواصل مرئية بين الأقسام */
.settings-section:not(:last-child)::after {
    content: '';
    display: block;
    height: 1px;
    background: linear-gradient(to right, #e2e8f0, transparent);
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
}

/* عناوين الأقسام */
.settings-title {
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e4e7;
    color: #1d2327;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.settings-title .section-icon {
    margin-left: 10px;
    color: inherit;
}

/* عناوين فرعية */
.settings-section h3 {
    color: #1d2327;
    font-size: 16px;
    font-weight: 500;
    margin: 1.5em 0 1em;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.settings-section h3 .subsection-icon {
    margin-left: 8px;
    color: #3730a3;
    font-size: 16px;
}

/* ======= تحسين كفاءة المساحة ======= */

/* مجموعات الإعدادات */
.settings-group {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

/* مجموعة إعدادات أفقية */
.settings-group-horizontal {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    align-items: flex-start;
}

/* حقول الإعدادات */
.settings-field {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.settings-field:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* تصغير حجم العناصر */
.settings-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #1d2327;
    font-size: 14px;
}

.settings-field .description {
    margin-top: 5px;
    color: #64748b;
    font-size: 12px;
    line-height: 1.5;
}

/* حقول الإدخال */
.settings-field input[type="text"],
.settings-field input[type="number"],
.settings-field input[type="url"],
.settings-field select,
.settings-field textarea {
    width: 100%;
    max-width: 100%;
    margin: 0;
    box-sizing: border-box;
    padding: 8px 12px;
    height: 38px;
    font-size: 14px;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.settings-field input[type="text"]:focus,
.settings-field input[type="number"]:focus,
.settings-field input[type="url"]:focus,
.settings-field select:focus,
.settings-field textarea:focus {
    border-color: #3730a3;
    box-shadow: 0 0 0 2px rgba(55, 48, 163, 0.2);
    outline: none;
}

.settings-field textarea {
    height: auto;
    min-height: 80px;
    padding: 10px 12px;
}

/* ======= تحسين تجربة المستخدم ======= */

/* أزرار التبديل */
.toggle-switch-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.switch-toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 22px;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: .3s;
    border-radius: 22px;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

input:checked + .toggle-slider {
    background-color: #3730a3;
}

input:checked + .toggle-slider:before {
    transform: translateX(22px);
}

.rtl input:checked + .toggle-slider:before {
    transform: translateX(-22px);
}

/* تلميحات نصية */
.tooltip-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #646970;
    color: #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 16px;
    font-size: 11px;
    margin-right: 5px;
    cursor: help;
    position: relative;
}

.tooltip-content {
    position: absolute;
    top: -5px;
    right: 25px;
    width: 200px;
    background-color: #1d2327;
    color: #fff;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    pointer-events: none;
}

.tooltip-icon:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
    top: -10px;
}

.tooltip-content:after {
    content: '';
    position: absolute;
    top: 15px;
    right: -5px;
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 5px solid #1d2327;
}

/* زر العودة للأعلى */
.back-to-top {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 40px;
    height: 40px;
    background-color: #2271b1;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: #135e96;
}

/* تحسينات إضافية */
.settings-field-inline {
    display: flex;
    align-items: center;
}

.settings-field-inline label {
    margin-bottom: 0;
    margin-left: 10px;
    min-width: 100px;
}

/* تنسيق التنقل السلس */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 32px;
}

/* أقسام قابلة للطي */
.collapsible-section {
    margin-bottom: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.collapsible-section:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.collapsible-header {
    padding: 12px 18px;
    background-color: #f8fafc;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
}

.collapsible-header:hover {
    background-color: #f1f5f9;
}

.collapsible-header h3, .collapsible-header h5 {
    margin: 0;
    padding: 0;
    border: none;
    font-size: 15px;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: #1e293b;
}

.collapsible-header h5 {
    font-size: 14px;
}

.collapsible-content {
    padding: 18px;
    border-top: 1px solid #e2e8f0;
    display: none;
    background-color: #fff;
}

.collapsible-section.open .collapsible-content {
    display: block;
}

.collapsible-toggle, .collapsible-header .dashicons {
    transition: transform 0.3s ease;
    color: #64748b;
}

.collapsible-section.open .collapsible-toggle,
.collapsible-section.open .collapsible-header .dashicons {
    transform: rotate(180deg);
    color: #3730a3;
}

/* إعدادات الحماية من الطلبات الوهمية */
.settings-field h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #3730a3;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 10px;
}

.phone-validation-options, .limit-orders-options {
    background-color: #f9fafb;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
    border: 1px solid #e5e7eb;
}

/* تنسيق ترتيب عناصر النموذج */
.form-elements-order-section {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.form-elements-order-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    border: none;
}

.form-elements-order-section .description {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 15px;
}

.form-elements-sortable {
    margin: 0;
    padding: 0;
    list-style: none;
}

.form-element-item {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    cursor: grab;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.form-element-item:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-element-item-with-children {
    background-color: #f0f6fc;
    border-color: #3730a3;
}

.element-handle {
    cursor: grab;
    color: #64748b;
    margin-left: 8px;
}

.element-icon {
    margin-left: 10px;
    color: #3730a3;
}

.element-label {
    flex: 1;
    font-weight: 500;
    color: #1e293b;
}

/* تنسيقات عنصر حقول النموذج القابل للطي */
.toggle-fields-icon {
    margin-right: auto;
    color: #3730a3;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: inline-block;
}

.toggle-fields-icon.open {
    transform: rotate(180deg);
}

.form-fields-container {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    margin: -5px 0 15px 0;
    padding: 15px;
    list-style: none;
    margin-right: 24px;
}

.form-fields-list h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #3730a3;
    font-size: 15px;
    font-weight: 500;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 8px;
}

/* تنسيق حقول النموذج - تم تحسينه لتقليل الارتفاع وتنظيم العناصر أفقياً */
.form-field-row {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 4px 8px;
    margin-bottom: 4px;
    cursor: grab;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
    position: relative;
    overflow: hidden;
}

.form-field-row:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transform: translateY(-1px);
}

.form-field-row:active {
    cursor: grabbing;
}

.form-field-row::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background-color: #3730a3;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-field-row:hover::before {
    opacity: 1;
}

/* تحسين تنسيق رأس الحقل - تنظيم أفقي */
.field-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 0;
    margin-bottom: 0;
    border-bottom: none;
}

.field-header h3 {
    margin: 0;
    font-size: 13px;
    font-weight: 500;
    color: #1e293b;
    display: flex;
    align-items: center;
    border: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.field-header h3 span {
    font-size: 11px;
    color: #64748b;
    margin-right: 6px;
    background-color: #f8fafc;
    padding: 1px 4px;
    border-radius: 3px;
    border: 1px solid #e2e8f0;
}

/* تحسين تنسيق أزرار الإجراءات */
.field-actions {
    display: flex;
    gap: 4px;
    margin-right: 0;
}

.field-actions span {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8fafc;
    border-radius: 3px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e2e8f0;
    font-size: 12px;
}

.field-actions span:hover {
    background-color: #3730a3;
    color: #fff;
    border-color: #3730a3;
}

.field-actions .edit-field:hover {
    background-color: #0ea5e9;
    border-color: #0ea5e9;
}

.field-actions .clone-field:hover {
    background-color: #10b981;
    border-color: #10b981;
}

.field-actions .delete-field:hover {
    background-color: #ef4444;
    border-color: #ef4444;
}

.handle {
    cursor: grab;
    color: #94a3b8;
    margin-left: 4px;
    transition: color 0.2s ease;
    font-size: 14px;
}

.handle:hover {
    color: #3730a3;
}

/* تحسين تنسيق تفاصيل الحقل - عرض أفقي */
.field-details {
    font-size: 11px;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 8px;
    border-top: none;
    padding-top: 0;
    margin-top: 4px;
}

.required-field {
    color: #ef4444;
    font-weight: 500;
}

.optional-field {
    color: #64748b;
}

.visible-field {
    color: #10b981;
}

.hidden-field {
    color: #ef4444;
}

/* تنسيق زر إضافة حقل */
.field-buttons {
    margin-top: 15px;
}

#add-field-button {
    display: inline-flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    color: #1e293b;
    padding: 8px 14px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

#add-field-button:hover {
    background-color: #f8fafc;
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#add-field-button .dashicons {
    margin-left: 5px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* تنسيق إعدادات حقول النموذج */
.fields-settings-container {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.fields-settings-container h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 15px 0;
}

.form-layout-section h5,
.column-settings-container h5 {
    font-size: 14px;
    font-weight: 500;
    color: #1e293b;
    margin: 0 0 10px 0;
}

/* تنسيق خيارات التخطيط - تم تحسينه ليكون أصغر ومتجاورة أفقياً */
.form-layout-preview {
    display: flex;
    flex-direction: row;
    gap: 5px;
    margin-bottom: 5px;
}

.layout-option {
    flex: 1;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 5px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    min-width: 0;
}

.layout-option:hover {
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.layout-option.active {
    border-color: #3730a3;
    background-color: #f0f7ff;
    box-shadow: 0 0 0 1px rgba(55, 48, 163, 0.2);
}

.layout-preview {
    margin-bottom: 3px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.layout-icon {
    margin-bottom: 2px;
    color: #64748b;
    font-size: 12px;
}

.layout-preview-items {
    width: 90%;
    margin: 0 auto;
}

.layout-preview-items span.preview-field {
    display: block;
    height: 4px;
    background-color: #cbd5e1;
    border-radius: 2px;
    margin-bottom: 2px;
}

.layout-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
}

.layout-responsive .desktop-view {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    margin-bottom: 3px;
}

.layout-responsive .mobile-view {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2px;
}

.layout-responsive .mobile-view span.preview-field {
    width: 90%;
    margin: 0 auto 2px;
}

.layout-label {
    font-size: 10px;
    font-weight: 500;
    color: #1e293b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.layout-radio {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.layout-description {
    font-size: 10px;
    color: #64748b;
    margin-top: 3px;
}

/* تنسيق خيارات المسافة بين الأعمدة - تم تحسينه ليكون أصغر ومتجاورة أفقياً */
.spacing-options {
    display: flex;
    flex-direction: row;
    gap: 5px;
    margin-bottom: 5px;
}

.spacing-option {
    flex: 1;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 5px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    min-width: 0;
}

.spacing-option:hover {
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.spacing-option.active {
    border-color: #3730a3;
    box-shadow: 0 0 0 1px rgba(55, 48, 163, 0.2);
}

.spacing-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 12px;
    margin-bottom: 3px;
}

.spacing-preview span {
    display: block;
    width: 10px;
    height: 10px;
    background-color: #cbd5e1;
    border-radius: 2px;
}

.spacing-preview.small-gap span:first-child {
    margin-left: 2px;
}

.spacing-preview.medium-gap span:first-child {
    margin-left: 4px;
}

.spacing-preview.large-gap span:first-child {
    margin-left: 6px;
}

.spacing-label {
    font-size: 10px;
    font-weight: 500;
    color: #1e293b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.spacing-radio {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.spacing-description {
    font-size: 11px;
    color: #64748b;
    margin-top: 6px;
}

/* تنسيق حالة السحب والإفلات */
.ui-sortable-placeholder {
    border: 2px dashed #cbd5e1;
    background-color: #f8fafc;
    visibility: visible !important;
    height: 40px;
    border-radius: 6px;
    margin-bottom: 10px;
}

.ui-sortable-helper {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    opacity: 0.9;
}

/* تحسينات للشاشات الصغيرة */
@media screen and (max-width: 782px) {
    .form-settings-container {
        flex-direction: column;
        gap: 20px;
    }

    .settings-sidebar {
        position: relative;
        top: 0;
        width: 100%;
        flex: none;
        height: auto;
        margin-bottom: 20px;
    }

    .settings-content {
        padding: 20px;
    }

    .settings-group {
        grid-template-columns: 1fr;
    }

    /* تحسين عرض خيارات التخطيط على الشاشات الصغيرة */
    .form-layout-preview {
        flex-wrap: wrap;
    }

    .layout-option {
        min-width: 80px;
    }

    .spacing-options {
        flex-wrap: wrap;
    }

    .spacing-option {
        min-width: 70px;
    }
}

/* تحسينات خاصة بالشريط الجانبي للإعدادات - تصميم مضغوط ومنظم */
.form-fields-management-layout {
    display: flex;
    gap: 20px;
    margin: 15px 0;
    direction: ltr;
    align-items: flex-start;
}

.fields-settings-sidebar {
    flex: 0 0 300px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    height: fit-content;
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 100px);
    overflow-y: auto;
}

.fields-settings-sidebar h4 {
    margin: 0 0 12px 0;
    color: #1e293b;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 6px;
}

.fields-list-main {
    flex: 1;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
}

.fields-list-main h4 {
    margin: 0 0 15px 0;
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
}

/* تحسين تنسيق الإعدادات داخل الشريط الجانبي - تصميم مضغوط */
.fields-settings-sidebar .fields-settings-container {
    padding: 0;
    margin: 0;
}

.fields-settings-sidebar h5 {
    font-size: 11px;
    font-weight: 600;
    color: #374151;
    margin: 10px 0 4px 0;
    padding: 4px 6px;
    background: #e5e7eb;
    border-radius: 3px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.fields-settings-sidebar h5:first-child {
    margin-top: 0;
}

/* تحسين قسم تخطيط الحقول - تصميم مضغوط جداً */
.fields-settings-sidebar .form-layout-section {
    margin-bottom: 8px;
    padding: 6px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.fields-settings-sidebar .tw-mb-2 {
    margin-bottom: 6px !important;
}

.fields-settings-sidebar .tw-text-xs {
    font-size: 10px !important;
}

.fields-settings-sidebar .tw-mb-1 {
    margin-bottom: 3px !important;
}

.fields-settings-sidebar .tw-mt-1 {
    margin-top: 3px !important;
}

.fields-settings-sidebar .tw-bg-gray-50 {
    background-color: #f9fafb !important;
}

.fields-settings-sidebar .tw-p-2 {
    padding: 4px !important;
}

.fields-settings-sidebar .tw-rounded {
    border-radius: 3px !important;
}

.fields-settings-sidebar .tw-border {
    border-width: 1px !important;
}

.fields-settings-sidebar .tw-border-gray-200 {
    border-color: #e5e7eb !important;
}

.fields-settings-sidebar .form-layout-preview {
    display: flex;
    gap: 4px;
    margin: 6px 0;
}

.fields-settings-sidebar .layout-option {
    flex: 1;
    padding: 3px 1px;
    border: 1px solid #d1d5db;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    background: #f9fafb;
    min-height: 32px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* تحسين أيقونات التخطيط */
.fields-settings-sidebar .layout-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;
}

.fields-settings-sidebar .layout-icon {
    font-size: 12px;
    color: #6b7280;
}

.fields-settings-sidebar .layout-icon .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.fields-settings-sidebar .layout-preview-items {
    display: flex;
    gap: 1px;
    margin-top: 1px;
}

.fields-settings-sidebar .preview-field {
    width: 8px;
    height: 3px;
    background: #d1d5db;
    border-radius: 1px;
}

.fields-settings-sidebar .layout-columns .preview-field {
    width: 6px;
}

.fields-settings-sidebar .layout-responsive {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.fields-settings-sidebar .desktop-view {
    display: flex;
    gap: 1px;
}

.fields-settings-sidebar .mobile-view {
    display: flex;
    gap: 1px;
}

.fields-settings-sidebar .mobile-view .preview-field {
    width: 12px;
}

.fields-settings-sidebar .layout-option:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.fields-settings-sidebar .layout-option.active {
    border-color: #3b82f6;
    background: #dbeafe;
}

.fields-settings-sidebar .layout-label {
    font-size: 9px;
    font-weight: 500;
    color: #374151;
    margin-top: 2px;
    display: block;
}

.fields-settings-sidebar .layout-description {
    font-size: 9px;
    color: #6b7280;
    margin: 4px 0 6px 0;
    line-height: 1.2;
}

/* تحسين إعدادات المسافات */
.fields-settings-sidebar .column-settings-container {
    margin-top: 6px;
    padding: 6px;
    background: #f3f4f6;
    border-radius: 3px;
    border: 1px solid #d1d5db;
}

.fields-settings-sidebar .spacing-options {
    display: flex;
    gap: 3px;
    margin: 4px 0;
}

.fields-settings-sidebar .spacing-option {
    flex: 1;
    padding: 2px 1px;
    border: 1px solid #d1d5db;
    border-radius: 2px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    background: #fff;
    min-height: 24px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* تحسين معاينة المسافات */
.fields-settings-sidebar .spacing-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    margin-bottom: 1px;
}

.fields-settings-sidebar .spacing-preview span {
    width: 4px;
    height: 8px;
    background: #9ca3af;
    border-radius: 1px;
}

.fields-settings-sidebar .spacing-preview.small-gap {
    gap: 1px;
}

.fields-settings-sidebar .spacing-preview.medium-gap {
    gap: 2px;
}

.fields-settings-sidebar .spacing-preview.large-gap {
    gap: 3px;
}

.fields-settings-sidebar .spacing-option:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.fields-settings-sidebar .spacing-option.active {
    border-color: #3b82f6;
    background: #dbeafe;
}

.fields-settings-sidebar .spacing-label {
    font-size: 8px;
    font-weight: 500;
    color: #374151;
    margin-top: 1px;
    display: block;
}

/* تحسين قسم تنسيق الحقول */
.fields-settings-sidebar .form-fields-styling-section {
    margin-bottom: 12px;
    padding: 8px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.fields-settings-sidebar .settings-field-row {
    margin-bottom: 8px;
}

.fields-settings-sidebar .settings-field {
    margin-bottom: 6px;
    padding: 6px;
    background: #f9fafb;
    border-radius: 3px;
    border: 1px solid #e5e7eb;
}

.fields-settings-sidebar .settings-field label {
    font-size: 10px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 3px;
    display: block;
}

.fields-settings-sidebar .settings-field input[type="range"] {
    width: 100%;
    height: 4px;
    margin: 4px 0;
}

.fields-settings-sidebar .settings-field output {
    font-size: 9px;
    color: #6b7280;
    font-weight: 500;
}

.fields-settings-sidebar .settings-field input[type="text"],
.fields-settings-sidebar .settings-field select {
    width: 100%;
    padding: 4px 6px;
    font-size: 10px;
    border: 1px solid #d1d5db;
    border-radius: 3px;
    background: #fff;
}

/* تحسين قسم الأيقونات والعناوين */
.fields-settings-sidebar .form-icons-labels-section {
    margin-bottom: 12px;
    padding: 8px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.fields-settings-sidebar .toggle-switch-container {
    display: flex;
    align-items: center;
    gap: 6px;
}

.fields-settings-sidebar .switch-toggle {
    position: relative;
    display: inline-block;
    width: 32px;
    height: 18px;
}

.fields-settings-sidebar .switch-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.fields-settings-sidebar .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 18px;
}

.fields-settings-sidebar .toggle-slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.fields-settings-sidebar input:checked + .toggle-slider {
    background-color: #3b82f6;
}

.fields-settings-sidebar input:checked + .toggle-slider:before {
    transform: translateX(14px);
}

.fields-settings-sidebar .toggle-label {
    font-size: 10px;
    font-weight: 500;
    color: #374151;
}

/* تحسين شريط التمرير للإعدادات */
.fields-settings-sidebar::-webkit-scrollbar {
    width: 4px;
}

.fields-settings-sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.fields-settings-sidebar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.fields-settings-sidebar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* تحسينات للشاشات الصغيرة - التخطيط الجديد */
@media screen and (max-width: 782px) {
    .form-fields-management-layout {
        flex-direction: column;
        gap: 12px;
    }

    .fields-settings-sidebar {
        flex: none;
        position: relative;
        top: 0;
        order: 2;
        max-height: none;
        padding: 12px;
    }

    .fields-list-main {
        order: 1;
        padding: 15px;
    }

    .fields-settings-sidebar h4,
    .fields-list-main h4 {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .fields-settings-sidebar .form-layout-preview {
        flex-wrap: wrap;
        gap: 6px;
    }

    .fields-settings-sidebar .layout-option {
        min-width: 60px;
        padding: 6px 4px;
    }

    .fields-settings-sidebar .spacing-options {
        flex-wrap: wrap;
        gap: 4px;
    }

    .fields-settings-sidebar .spacing-option {
        min-width: 50px;
        padding: 4px 2px;
    }
}

/* تحسينات RTL */
.rtl .settings-navigation a {
    border-right: none;
    border-left: 3px solid transparent;
}

.rtl .settings-navigation a:hover {
    border-left-color: #cdcdcd;
}

.rtl .settings-navigation a.active {
    border-left-color: #2271b1;
}

.rtl .settings-navigation a .nav-icon {
    margin-left: 0;
    margin-right: 8px;
}

.rtl .save-form-settings .dashicons {
    margin-left: 0;
    margin-right: 5px;
}

.rtl .settings-title .section-icon {
    margin-left: 0;
    margin-right: 8px;
}

.rtl .settings-section h3 .subsection-icon {
    margin-left: 0;
    margin-right: 6px;
}

.rtl .tooltip-icon {
    margin-right: 0;
    margin-left: 5px;
}

.rtl .tooltip-content {
    right: auto;
    left: 25px;
}

.rtl .tooltip-content:after {
    right: auto;
    left: -5px;
    border-left: none;
    border-right: 5px solid #1d2327;
}

.rtl .back-to-top {
    left: auto;
    right: 20px;
}

.rtl .settings-field-inline label {
    margin-left: 0;
    margin-right: 10px;
}

/* ======= إعدادات السلة ======= */

/* إخفاء/إظهار خيارات السلة حسب حالة التفعيل */
.cart-button-options,
.cart-floating-options,
.cart-advanced-options {
    transition: all 0.3s ease;
}

.cart-button-options[style*="display: none"],
.cart-floating-options[style*="display: none"],
.cart-advanced-options[style*="display: none"] {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
}

/* تنسيق خاص لإعدادات السلة */
#cart-settings .settings-field {
    margin-bottom: 20px;
}

#cart-settings .collapsible-section {
    margin-bottom: 25px;
}

#cart-settings .color-picker {
    width: 100px;
}

/* أيقونة السلة في القائمة الجانبية */
.nav-icon.dashicons-cart {
    color: #28a745;
}

/* تنسيق إعدادات السلة المتقدمة */
.cart-advanced-options .settings-field,
.cart-button-options .settings-field {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

/* تحسين عرض أزرار التبديل في إعدادات السلة */
#cart-settings .toggle-switch-container {
    margin: 8px 0;
}

#cart-settings .toggle-switch-container label.switch-toggle {
    margin-left: 10px;
}
