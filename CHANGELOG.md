# سجل التغييرات - Form Elrakami

## [1.2.0] - 2024-12-19

### ✨ ميزة جديدة مبسطة: رسوم التوصيل الإضافية المباشرة
- **ميتا بوكس جديد**: إضافة رسوم توصيل إضافية مباشرة من صفحة تحرير المنتج
- **سهولة الاستخدام**: لا حاجة لإعداد فئات شحن معقدة
- **مرونة كاملة**: رسوم مختلفة لكل منتج أو متغير بشكل فردي
- **دعم المتغيرات**: إضافة رسوم مختلفة لكل متغير من المنتج
- **عمود في قائمة المنتجات**: عرض سريع للمنتجات التي لها رسوم إضافية

### 🎨 تحسينات واجهة المستخدم
- **تصميم واضح**: قسم منفصل في تبويب الشحن مع أيقونة مميزة
- **رسائل توضيحية**: شرح كيفية عمل الرسوم الإضافية
- **حقول بسيطة**: مبلغ + وصف اختياري للرسوم
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الشاشات

### 🔧 تحسينات تقنية
- **تكامل شامل**: يعمل مع جميع طرق الشحن في الإضافة
- **حساب تلقائي**: التكلفة النهائية = التكلفة الأساسية + الرسوم الإضافية
- **دعم كامل للمتغيرات**: رسوم منفصلة لكل متغير
- **تسجيل مفصل**: رسائل تسجيل واضحة لتتبع العمليات

## [1.1.0] - 2024-12-19

### ✨ ميزات جديدة
- **دعم فئات الشحن (Shipping Classes)**: إضافة دعم كامل لفئات الشحن في WooCommerce
  - إمكانية تحديد تكاليف شحن إضافية لكل فئة منتجات
  - واجهة إدارة سهلة لتحديد تكاليف الفئات
  - تطبيق تلقائي للتكاليف الإضافية على المنتجات المصنفة
  - دعم جميع طرق الشحن الموجودة في الإضافة

### 🔧 تحسينات
- تحسين حساب تكاليف الشحن ليشمل فئات الشحن
- إضافة رسائل تسجيل مفصلة لتتبع عمليات حساب التكاليف
- تحسين واجهة المستخدم في صفحة إدارة مناطق الشحن
- إضافة دليل شامل لاستخدام فئات الشحن
- إضافة نظام اختبار للتأكد من عمل الميزات بشكل صحيح

### 🛠️ تحديثات تقنية
- تحديث دالة `get_woocommerce_shipping_methods` لدعم فئات الشحن
- تحديث دالة `get_default_shipping_methods` لدعم فئات الشحن
- تحديث نظام السلة المخصص لحساب تكاليف فئات الشحن
- إضافة دالة `calculate_shipping_class_cost` لحساب التكاليف الإضافية
- تحديث طرق الشحن الموجودة تلقائياً عند تغيير إعدادات الفئات

### 📚 توثيق
- إضافة دليل شامل لاستخدام فئات الشحن (`SHIPPING_CLASSES_GUIDE.md`)
- إضافة أمثلة عملية لفئات الشحن الشائعة
- إضافة نصائح لاستكشاف الأخطاء وحلها
- تحديث التوثيق الفني للمطورين

### 🧪 اختبارات
- إضافة مجموعة اختبارات شاملة لفئات الشحن
- اختبار حساب التكاليف الإضافية
- اختبار تكامل WooCommerce
- اختبار حفظ واسترجاع الإعدادات

### 🎨 واجهة المستخدم
- إضافة قسم "إعدادات فئات الشحن" في صفحة إدارة مناطق الشحن
- تصميم جدول تفاعلي لإدارة تكاليف الفئات
- إضافة رسائل توضيحية وأمثلة عملية
- تحسين التصميم المتجاوب للشاشات الصغيرة
- إضافة أيقونات ومؤشرات بصرية

### 🔄 نظام التحديث
- إضافة نظام تحديث تلقائي للمستخدمين الحاليين
- تحديث طرق الشحن الموجودة لدعم الميزات الجديدة
- إضافة إشعارات للمدراء حول الميزات الجديدة
- ضمان التوافق مع الإعدادات الحالية

### 🐛 إصلاحات
- إصلاح مشكلة عدم احتساب التكاليف الإضافية في بعض الحالات
- تحسين معالجة الأخطاء في حساب تكاليف الشحن
- إصلاح مشاكل التوافق مع إصدارات WooCommerce المختلفة

---

## كيفية الاستفادة من الميزات الجديدة

### إعداد فئات الشحن:
1. اذهب إلى **WooCommerce > الإعدادات > الشحن > الفئات**
2. أضف فئات جديدة (مثل "المنتجات الثقيلة")
3. اذهب إلى **Form Elrakami > إدارة مناطق الشحن**
4. حدد التكاليف الإضافية في قسم "إعدادات فئات الشحن"
5. طبق الفئات على منتجاتك من صفحة تحرير المنتج

### أمثلة على الاستخدام:
- **المنتجات الثقيلة**: +200 دج
- **المنتجات الهشة**: +150 دج
- **الأجهزة الإلكترونية**: +100 دج

### اختبار النظام:
- استخدم رابط "تشغيل اختبارات فئات الشحن" في صفحة إدارة مناطق الشحن
- تحقق من ظهور التكاليف الصحيحة في النموذج
- اختبر مع منتجات من فئات مختلفة

---

## متطلبات النظام
- WordPress 5.0 أو أحدث
- WooCommerce 3.0 أو أحدث
- PHP 7.4 أو أحدث

---

## 🔄 مقارنة بين النظامين

### الإصدار 1.2.0 - رسوم التوصيل الإضافية المباشرة (الموصى به)
✅ **سهل الاستخدام**: إضافة مباشرة من صفحة المنتج
✅ **مرن**: رسوم مختلفة لكل منتج/متغير
✅ **سريع**: لا حاجة لإعدادات معقدة
✅ **واضح**: عرض مباشر في قائمة المنتجات

### الإصدار 1.1.0 - فئات الشحن (للاستخدام المتقدم)
⚙️ **منظم**: تجميع المنتجات في فئات
⚙️ **موحد**: نفس الرسوم لجميع منتجات الفئة
⚙️ **معقد**: يتطلب إعداد فئات في WooCommerce أولاً

**التوصية**: استخدم النظام الجديد (1.2.0) للبساطة والمرونة، أو النظام القديم (1.1.0) إذا كنت تحتاج تنظيم المنتجات في فئات موحدة.

---

## الدعم الفني
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة:
- دليل رسوم التوصيل الإضافية المباشرة
- دليل استخدام فئات الشحن (للنظام المتقدم)
- سجلات الأخطاء في WordPress
- فريق الدعم الفني
