/**
 * تنسيقات صفحة قائمة العملاء
 */

/* تنسيق النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 25px;
    border: 1px solid #ddd;
    width: 450px;
    max-width: 90%;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    position: relative;
}

.modal-close {
    position: absolute;
    top: 15px;
    left: 15px;
    font-size: 22px;
    cursor: pointer;
    color: #666;
    transition: color 0.2s;
}

.modal-close:hover {
    color: #d63638;
}

.modal-title {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    font-size: 18px;
    color: #23282d;
}

.modal-form-row {
    margin-bottom: 20px;
}

.modal-form-row label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #23282d;
}

.modal-form-row input,
.modal-form-row select,
.modal-form-row textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.modal-form-row input:focus,
.modal-form-row select:focus,
.modal-form-row textarea:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.modal-form-row textarea {
    min-height: 80px;
}

.modal-actions {
    text-align: left;
    margin-top: 25px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-actions .button {
    padding: 8px 16px;
    min-width: 80px;
}

/* تنسيق الجدول */
.customers-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.customers-table th,
.customers-table td {
    padding: 10px;
    text-align: right;
}

.customers-table th {
    background-color: #f0f0f1;
    font-weight: 600;
}

.customers-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.customers-table tr:hover {
    background-color: #f0f6fc;
}

/* تنسيق حالة العميل */
.status-active {
    color: #d63638;
    font-weight: bold;
}

.status-inactive {
    color: #2271b1;
}

/* تنسيق فلتر البحث */
.customers-filter {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.customers-filter select,
.customers-filter input[type="search"] {
    min-width: 200px;
}

/* تنسيق نموذج إضافة/تعديل العميل */
.customer-form-card {
    background-color: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.customer-form-card h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f1;
}

/* تنسيق الإجراءات */
.row-actions {
    color: #787c82;
    font-size: 13px;
    visibility: hidden;
}

.customers-table tr:hover .row-actions {
    visibility: visible;
}

.row-actions span {
    padding: 0 4px;
}

.row-actions span:first-child {
    padding-right: 0;
}

.row-actions span.delete a {
    color: #d63638;
}

.row-actions span.block a {
    color: #d63638;
}

.row-actions span.unblock a {
    color: #2271b1;
}

.row-actions span.edit a {
    color: #2271b1;
}

/* تنسيق الأعمدة */
.customers-table .column-id {
    width: 5%;
}

.customers-table .column-name {
    width: 15%;
}

.customers-table .column-phone {
    width: 10%;
}

.customers-table .column-ip {
    width: 10%;
}

.customers-table .column-email {
    width: 15%;
}

.customers-table .column-reason {
    width: 20%;
}

.customers-table .column-date {
    width: 10%;
}

.customers-table .column-status {
    width: 5%;
}

.customers-table .column-actions {
    width: 10%;
}

/* تنسيق للشاشات الصغيرة */
@media screen and (max-width: 782px) {
    .customers-filter {
        flex-direction: column;
        align-items: flex-start;
    }

    .customers-filter select,
    .customers-filter input[type="search"] {
        width: 100%;
    }

    .customers-table .column-reason,
    .customers-table .column-email,
    .customers-table .column-date {
        display: none;
    }
}
