/**
 * JavaScript لإدارة إعدادات اللغة
 */
jQuery(document).ready(function($) {
    // عند تغيير اللغة
    $('#form_elrakami_language').on('change', function() {
        var selectedLanguage = $(this).val();
        
        // تحديث حالة اللغات في القائمة
        $('.language-list li').removeClass('active');
        $('.language-list li[data-lang="' + selectedLanguage + '"]').addClass('active');
        
        // إظهار رسالة تأكيد
        if (selectedLanguage !== 'auto') {
            var languageName = $('option:selected', this).text();
            var message = 'تم اختيار اللغة: ' + languageName + '. لا تنس حفظ الإعدادات لتطبيق التغييرات.';
            
            // إنشاء عنصر الإشعار إذا لم يكن موجوداً
            if ($('#language-change-notice').length === 0) {
                $('<div id="language-change-notice" class="notice notice-info is-dismissible"><p>' + message + '</p></div>')
                    .insertAfter('#language-settings h2');
            } else {
                $('#language-change-notice p').text(message);
            }
        } else {
            // إزالة الإشعار إذا تم اختيار "تلقائي"
            $('#language-change-notice').remove();
        }
    });
    
    // إزالة الإشعار عند النقر على زر الإغلاق
    $(document).on('click', '#language-change-notice .notice-dismiss', function() {
        $('#language-change-notice').remove();
    });
});
