/* أنماط ملخص الطلب مع زر التبديل */
.total-price-container {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.total-price-container:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 14px;
    background-color: #f1f5f9;
    border-bottom: 1px solid #e2e8f0;
    cursor: pointer;
}

.summary-title {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.summary-title i {
    color: #3730a3;
    font-size: 14px;
}

.summary-toggle-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.summary-toggle-btn:hover {
    background-color: #e2e8f0;
    color: #1e293b;
}

.summary-toggle-btn i {
    transition: transform 0.3s ease;
}

.summary-toggle-btn.collapsed i {
    transform: rotate(180deg);
}

.price-details {
    padding: 12px;
    background-color: #fff;
    transition: max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease;
    overflow: hidden;
}

.price-details.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px dashed #e2e8f0;
}

.price-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.price-row.total {
    font-weight: bold;
    color: #1e293b;
    border-top: 1px solid #e2e8f0;
    padding-top: 8px;
    margin-top: 6px;
}

.price-label {
    color: #475569;
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    font-size: 13px;
}

.price-label i {
    color: #3730a3;
    width: 16px;
    text-align: center;
    font-size: 12px;
}

.price-row.total .price-label i {
    color: #3730a3;
}

.product-price-display,
.shipping-price-display,
.total-price-display {
    font-weight: 600;
    color: #3730a3;
    background-color: rgba(55, 48, 163, 0.05);
    padding: 3px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 13px;
}

.product-regular-price {
    font-size: 11px;
    color: #bbb;
    text-decoration: line-through;
    font-weight: 300;
    opacity: 0.8;
    margin-right: 8px;
    display: inline-block;
}

.product-price-display:hover,
.shipping-price-display:hover,
.total-price-display:hover {
    background-color: rgba(55, 48, 163, 0.1);
}

.total-price-display {
    color: #3730a3;
    font-size: 14px;
    background-color: rgba(55, 48, 163, 0.08);
    padding: 4px 8px;
}
