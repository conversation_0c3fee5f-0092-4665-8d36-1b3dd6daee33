/**
 * Responsive Hints CSS
 * 
 * Provides styling for responsive design hints in the form editor
 */

.responsive-hint {
    display: inline-block;
    margin-left: 10px;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    background-color: #0073aa;
}

.responsive-hint.mobile {
    background-color: #e74c3c;
}

.responsive-hint.tablet {
    background-color: #f39c12;
}

.responsive-hint.desktop {
    background-color: #27ae60;
}

.responsive-preview-controls {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.responsive-preview-controls button {
    margin: 0 5px;
    padding: 5px 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
}

.responsive-preview-controls button.active {
    background-color: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

.form-preview-container {
    transition: width 0.3s ease;
    margin: 0 auto;
    border: 1px solid #ddd;
    padding: 15px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.form-preview-container.mobile {
    width: 375px;
}

.form-preview-container.tablet {
    width: 768px;
}

.form-preview-container.desktop {
    width: 100%;
    max-width: 1200px;
}

/* Responsive field indicators */
.field-responsive-status {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
}

.device-visibility {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    border-radius: 50%;
    text-align: center;
    line-height: 16px;
    font-size: 10px;
    color: #fff;
}

.device-visibility.visible {
    background-color: #27ae60;
}

.device-visibility.hidden {
    background-color: #e74c3c;
    position: relative;
}

.device-visibility.hidden:after {
    content: '';
    position: absolute;
    top: 7px;
    left: 3px;
    width: 10px;
    height: 2px;
    background-color: #fff;
    transform: rotate(45deg);
}
