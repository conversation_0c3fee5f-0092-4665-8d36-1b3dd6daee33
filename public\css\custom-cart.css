/**
 * أنماط CSS للسلة المخصصة
 * Form Elrakami Custom Cart System
 */

/* أيقونة السلة العائمة */
.floating-cart-icon {
    position: fixed;
    z-index: 9999;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* مواضع الأيقونة العائمة */
.floating-cart-icon.top-left {
    top: 20px;
    left: 20px;
}

.floating-cart-icon.top-right {
    top: 20px;
    right: 20px;
}

.floating-cart-icon.bottom-left {
    bottom: 20px;
    left: 20px;
}

.floating-cart-icon.bottom-right {
    bottom: 20px;
    right: 20px;
}

.floating-cart-icon:hover {
    transform: scale(1.1);
}

.cart-icon-wrapper {
    position: relative;
    background: #28a745;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
}

/* أحجام الأيقونة العائمة */
.cart-icon-wrapper.small {
    width: 50px;
    height: 50px;
}

.cart-icon-wrapper.medium {
    width: 60px;
    height: 60px;
}

.cart-icon-wrapper.large {
    width: 70px;
    height: 70px;
}

/* أشكال الأيقونة العائمة */
.cart-icon-wrapper.circle {
    border-radius: 50%;
}

.cart-icon-wrapper.square {
    border-radius: 0;
}

.cart-icon-wrapper.rounded {
    border-radius: 12px;
}

.cart-icon-wrapper:hover {
    filter: brightness(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.cart-icon {
    width: 24px;
    height: 24px;
    color: white;
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    min-width: 24px;
}

.cart-count.hidden {
    display: none;
}

/* السلة الجانبية */
.cart-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.cart-sidebar.active {
    visibility: visible;
    opacity: 1;
}

.cart-sidebar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
}

.cart-sidebar-content {
    position: absolute;
    top: 0;
    left: -400px;
    width: 400px;
    height: 100%;
    background: white;
    display: flex;
    flex-direction: column;
    transition: left 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.cart-sidebar.active .cart-sidebar-content {
    left: 0;
}

/* رأس السلة */
.cart-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.cart-sidebar-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.cart-close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.cart-close-btn:hover {
    background: #e9ecef;
}

.cart-close-btn svg {
    width: 20px;
    height: 20px;
    color: #666;
}

/* جسم السلة */
.cart-sidebar-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* قسم معلومات العميل */
.customer-info-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.customer-info-section h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 8px;
}

.customer-info-form .form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
}

.customer-info-form .form-row:last-child {
    margin-bottom: 0;
}

.customer-info-form .form-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.customer-info-form .form-input:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.1);
}

.customer-info-form textarea.form-input {
    resize: vertical;
    min-height: 60px;
}

.save-customer-info-btn {
    width: 100%;
    padding: 10px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
    margin-top: 10px;
}

.save-customer-info-btn:hover {
    background: #218838;
}

.save-customer-info-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* قسم المنتجات */
.cart-items-section {
    margin-bottom: 20px;
}

.cart-items-section h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 8px;
}

.empty-cart {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-cart-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-cart p {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 500;
}

.empty-cart small {
    font-size: 14px;
    color: #999;
}

/* عناصر السلة */
.cart-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.cart-item {
    display: flex;
    gap: 12px;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.2s ease;
}

.cart-item:hover {
    border-color: #ddd;
    background: #f5f5f5;
}

.item-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #666;
    text-align: center;
}

.item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.item-name {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.item-price {
    font-size: 13px;
    color: #28a745;
    font-weight: 500;
}

.customer-info-summary {
    margin: 8px 0;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.customer-info-summary .info-item {
    font-size: 11px;
    color: #666;
    margin-bottom: 3px;
    line-height: 1.3;
}

.customer-info-summary .info-item:last-child {
    margin-bottom: 0;
}

.customer-info-summary .info-item strong {
    color: #333;
    font-weight: 600;
}

.customer-info-summary .shipping-info {
    background: #e8f5e8;
    border: 1px solid #28a745;
    border-radius: 3px;
    padding: 4px 6px;
    margin-top: 5px;
}

.customer-info-summary .shipping-info strong {
    color: #28a745;
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
}

.qty-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    color: #495057;
    transition: all 0.2s ease;
}

.qty-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.qty-input {
    width: 40px;
    height: 24px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
    padding: 0;
}

.item-total {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.total-price {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.remove-item {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.remove-item:hover {
    background: #fee;
}

.remove-item svg {
    width: 16px;
    height: 16px;
    color: #dc3545;
}

/* تذييل السلة */
.cart-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    background: #f8f9fa;
}

.cart-totals {
    margin-bottom: 20px;
}

.cart-totals > div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.cart-subtotal,
.cart-shipping {
    color: #666;
}

.cart-total {
    font-weight: 600;
    font-size: 16px;
    color: #333;
    border-top: 1px solid #ddd;
    padding-top: 8px;
    margin-top: 12px;
}

.cart-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.btn-checkout,
.btn-continue-shopping {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.btn-checkout {
    background: #28a745;
    color: white;
}

.btn-checkout:hover {
    background: #218838;
}

.btn-continue-shopping {
    background: #6c757d;
    color: white;
}

.btn-continue-shopping:hover {
    background: #5a6268;
}

/* زر إضافة إلى السلة في النموذج */
.form-elrakami-add-to-cart {
    margin-top: 15px;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: 44px;
    width: 100%;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.form-elrakami-add-to-cart:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    filter: brightness(1.1);
}

.form-elrakami-add-to-cart:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.form-elrakami-add-to-cart:active {
    transform: translateY(0);
}

/* أيقونة السلة المدمجة بجانب زر الطلب */
.cart-icon-inline {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    font-size: 14px;
}

.cart-icon-inline:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.cart-icon-inline:active {
    transform: scale(0.95);
}

.cart-icon-inline.loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.form-elrakami-add-to-cart .button-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.form-elrakami-add-to-cart .button-icon i {
    font-size: 16px;
}

/* تأثيرات إضافية */
.cart-sidebar-content {
    backdrop-filter: blur(10px);
}

.cart-item {
    animation: slideInFromLeft 0.3s ease-out;
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.floating-cart-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* تحسينات إضافية للأزرار */
.btn-checkout,
.btn-continue-shopping,
.form-elrakami-add-to-cart {
    position: relative;
    overflow: hidden;
}

.btn-checkout::before,
.btn-continue-shopping::before,
.form-elrakami-add-to-cart::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-checkout:hover::before,
.btn-continue-shopping:hover::before,
.form-elrakami-add-to-cart:hover::before {
    left: 100%;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .cart-sidebar-content {
        width: 100%;
        left: -100%;
    }

    .floating-cart-icon {
        width: 50px;
        height: 50px;
        top: 15px;
        left: 15px;
    }

    .cart-icon-wrapper {
        width: 50px;
        height: 50px;
    }

    .cart-icon {
        width: 20px;
        height: 20px;
    }

    .cart-count {
        width: 20px;
        height: 20px;
        font-size: 10px;
        top: -3px;
        right: -3px;
    }

    .cart-sidebar-header,
    .cart-sidebar-body,
    .cart-sidebar-footer {
        padding: 15px;
    }

    .cart-item {
        padding: 12px;
    }

    .item-image {
        width: 50px;
        height: 50px;
    }

    .customer-info-form .form-row {
        flex-direction: column;
        gap: 8px;
    }

    .customer-info-form .form-input {
        font-size: 16px; /* منع التكبير في iOS */
    }
}
