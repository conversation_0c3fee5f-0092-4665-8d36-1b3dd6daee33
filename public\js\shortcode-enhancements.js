/**
 * تحسينات خاصة بالشورت كود
 * تحسين الأداء والتجاوب والشريط السفلي
 */

(function($) {
    'use strict';

    // متغيرات عامة
    let isScrolling = false;
    let scrollTimeout;
    let resizeTimeout;
    let stickyBarVisible = false;

    /**
     * تهيئة تحسينات الشورت كود
     */
    function initShortcodeEnhancements() {
        // تحسين الشريط السفلي للشورت كود
        initShortcodeStickyBar();

        // تحسين التجاوب
        initResponsiveEnhancements();

        // تحسين الأداء
        initPerformanceOptimizations();

        // تحسين عرض معلومات المنتج
        initProductInfoEnhancements();

        // إضافة دعم خيار الظهور الدائم
        initAlwaysVisibleOption();
    }

    /**
     * تحسين الشريط السفلي للشورت كود
     */
    function initShortcodeStickyBar() {
        const $shortcodeContainers = $('.form-elrakami-shortcode-wrapper');

        if ($shortcodeContainers.length === 0) return;

        $shortcodeContainers.each(function() {
            const $container = $(this);
            const $stickyBar = $container.find('.form-elrakami-sticky-bar');

            if ($stickyBar.length === 0) return;

            // تحسين منطق إظهار/إخفاء الشريط
            const throttledScrollHandler = throttle(function() {
                handleStickyBarVisibility($container, $stickyBar);
            }, 16); // 60fps

            $(window).on('scroll', throttledScrollHandler);
            $(window).on('resize', throttle(function() {
                handleStickyBarVisibility($container, $stickyBar);
            }, 250));

            // إظهار الشريط في البداية
            handleStickyBarVisibility($container, $stickyBar);
        });
    }

    /**
     * معالجة رؤية الشريط السفلي
     */
    function handleStickyBarVisibility($container, $stickyBar) {
        if (!$container.length || !$stickyBar.length) return;

        const containerTop = $container.offset().top;
        const containerBottom = containerTop + $container.outerHeight();
        const windowTop = $(window).scrollTop();
        const windowBottom = windowTop + $(window).height();

        // التحقق من إعدادات الشريط
        const alwaysVisible = $stickyBar.data('always-visible') === 'yes';

        // تحديد ما إذا كان النموذج مرئياً
        const isFormVisible = (containerBottom > windowTop) && (containerTop < windowBottom);

        if (alwaysVisible) {
            // إظهار الشريط دائماً
            showStickyBar($stickyBar);
        } else {
            // إظهار الشريط فقط عندما لا يكون النموذج مرئياً بالكامل
            if (isFormVisible && containerTop > windowTop && containerBottom < windowBottom) {
                hideStickyBar($stickyBar);
            } else {
                showStickyBar($stickyBar);
            }
        }
    }

    /**
     * إظهار الشريط السفلي
     */
    function showStickyBar($stickyBar) {
        if (!stickyBarVisible) {
            $stickyBar.removeClass('hidden');
            stickyBarVisible = true;
        }
    }

    /**
     * إخفاء الشريط السفلي
     */
    function hideStickyBar($stickyBar) {
        if (stickyBarVisible) {
            $stickyBar.addClass('hidden');
            stickyBarVisible = false;
        }
    }

    /**
     * تحسينات التجاوب
     */
    function initResponsiveEnhancements() {
        // تحسين عرض النموذج على الشاشات المختلفة
        function adjustFormLayout() {
            const $shortcodeContainers = $('.form-elrakami-shortcode-container');

            $shortcodeContainers.each(function() {
                const $container = $(this);
                const windowWidth = $(window).width();

                // تعديل التخطيط حسب حجم الشاشة
                if (windowWidth <= 480) {
                    $container.addClass('mobile-layout');
                    adjustMobileLayout($container);
                } else if (windowWidth <= 768) {
                    $container.addClass('tablet-layout').removeClass('mobile-layout');
                    adjustTabletLayout($container);
                } else {
                    $container.removeClass('mobile-layout tablet-layout');
                    adjustDesktopLayout($container);
                }
            });
        }

        // تطبيق التحسينات عند تحميل الصفحة
        adjustFormLayout();

        // تطبيق التحسينات عند تغيير حجم النافذة
        $(window).on('resize', throttle(adjustFormLayout, 250));
    }

    /**
     * تعديل التخطيط للهواتف المحمولة
     */
    function adjustMobileLayout($container) {
        // تحسين عرض معلومات المنتج
        const $productInfo = $container.find('.product-info-section');
        $productInfo.addClass('mobile-optimized');

        // تحسين الحقول
        const $fields = $container.find('.form-field');
        $fields.addClass('mobile-field');

        // تحسين الأزرار
        const $buttons = $container.find('.submit-button, .quantity-btn');
        $buttons.addClass('mobile-button');
    }

    /**
     * تعديل التخطيط للأجهزة اللوحية
     */
    function adjustTabletLayout($container) {
        const $productInfo = $container.find('.product-info-section');
        $productInfo.addClass('tablet-optimized');

        const $fields = $container.find('.form-field');
        $fields.addClass('tablet-field');
    }

    /**
     * تعديل التخطيط لأجهزة سطح المكتب
     */
    function adjustDesktopLayout($container) {
        // إزالة فئات الهاتف المحمول والجهاز اللوحي
        $container.find('*').removeClass('mobile-optimized tablet-optimized mobile-field tablet-field mobile-button');
    }

    /**
     * تحسينات الأداء
     */
    function initPerformanceOptimizations() {
        // تحسين التمرير
        optimizeScrollPerformance();

        // تحسين تغيير حجم النافذة
        optimizeResizePerformance();

        // تحسين تحديث DOM
        optimizeDOMUpdates();
    }

    /**
     * تحسين أداء التمرير
     */
    function optimizeScrollPerformance() {
        let ticking = false;

        function updateScrollElements() {
            // تحديث العناصر المرتبطة بالتمرير
            const $shortcodeContainers = $('.form-elrakami-shortcode-wrapper');

            $shortcodeContainers.each(function() {
                const $container = $(this);
                const $stickyBar = $container.find('.form-elrakami-sticky-bar');

                if ($stickyBar.length > 0) {
                    handleStickyBarVisibility($container, $stickyBar);
                }
            });

            ticking = false;
        }

        $(window).on('scroll', function() {
            if (!ticking) {
                requestAnimationFrame(updateScrollElements);
                ticking = true;
            }
        });
    }

    /**
     * تحسين أداء تغيير حجم النافذة
     */
    function optimizeResizePerformance() {
        $(window).on('resize', throttle(function() {
            // إعادة حساب التخطيط
            const $shortcodeContainers = $('.form-elrakami-shortcode-container');

            $shortcodeContainers.each(function() {
                const $container = $(this);

                // إعادة تعيين الأبعاد
                $container.css('height', 'auto');

                // إعادة حساب موضع الشريط السفلي
                const $stickyBar = $container.find('.form-elrakami-sticky-bar');
                if ($stickyBar.length > 0) {
                    handleStickyBarVisibility($container, $stickyBar);
                }
            });
        }, 250));
    }

    /**
     * تحسين تحديثات DOM
     */
    function optimizeDOMUpdates() {
        // استخدام DocumentFragment لتحديثات DOM المتعددة
        const observer = new MutationObserver(throttle(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // إعادة تهيئة التحسينات للعناصر الجديدة
                    const $newShortcodes = $(mutation.addedNodes).find('.form-elrakami-shortcode-wrapper');
                    if ($newShortcodes.length > 0) {
                        initShortcodeEnhancements();
                    }
                }
            });
        }, 100));

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * تحسين عرض معلومات المنتج
     */
    function initProductInfoEnhancements() {
        const $productSections = $('.form-elrakami-shortcode-container .product-info-section');

        $productSections.each(function() {
            const $section = $(this);

            // تحسين تحميل الصور
            const $image = $section.find('.product-image');
            if ($image.length > 0) {
                optimizeImageLoading($image);
            }

            // تحسين عرض النصوص
            optimizeTextDisplay($section);
        });
    }

    /**
     * تحسين تحميل الصور
     */
    function optimizeImageLoading($image) {
        // إضافة lazy loading
        $image.attr('loading', 'lazy');

        // إضافة placeholder أثناء التحميل
        $image.on('load', function() {
            $(this).addClass('loaded');
        });

        $image.on('error', function() {
            $(this).addClass('error').attr('alt', 'فشل في تحميل الصورة');
        });
    }

    /**
     * تحسين عرض النصوص
     */
    function optimizeTextDisplay($section) {
        // تحسين قراءة النصوص الطويلة
        const $title = $section.find('.product-title');
        const $description = $section.find('.product-description');

        if ($title.length > 0) {
            truncateText($title, 50);
        }

        if ($description.length > 0) {
            truncateText($description, 100);
        }
    }

    /**
     * اقتطاع النص الطويل
     */
    function truncateText($element, maxLength) {
        const originalText = $element.text();

        if (originalText.length > maxLength) {
            const truncatedText = originalText.substring(0, maxLength) + '...';
            $element.text(truncatedText);

            // إضافة tooltip للنص الكامل
            $element.attr('title', originalText);
        }
    }

    /**
     * دعم خيار الظهور الدائم
     */
    function initAlwaysVisibleOption() {
        // التحقق من إعدادات الظهور الدائم من الإعدادات العامة
        const alwaysVisible = typeof formElrakami !== 'undefined' &&
                             formElrakami.settings &&
                             formElrakami.settings.sticky_bar_always_visible === 'yes';

        // التحقق من إعدادات الشورت كود
        const shortcodeAlwaysVisible = typeof formElrakami !== 'undefined' &&
                                      formElrakami.settings &&
                                      formElrakami.settings.sticky_bar_shortcode_always_visible === 'yes';

        if (alwaysVisible || shortcodeAlwaysVisible) {
            $('.form-elrakami-sticky-bar').each(function() {
                $(this).attr('data-always-visible', 'yes');
            });
        }

        // للشورت كود، نطبق الإعداد الخاص
        $('.form-elrakami-shortcode-wrapper .form-elrakami-sticky-bar').each(function() {
            if (shortcodeAlwaysVisible) {
                $(this).attr('data-always-visible', 'yes');
            }
        });
    }

    /**
     * دالة throttle لتحسين الأداء
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * دالة debounce لتحسين الأداء
     */
    function debounce(func, wait, immediate) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // تهيئة التحسينات عند تحميل الصفحة
    $(document).ready(function() {
        initShortcodeEnhancements();
    });

    // إعادة تهيئة التحسينات عند تحميل المحتوى عبر AJAX
    $(document).on('ajaxComplete', function() {
        setTimeout(initShortcodeEnhancements, 100);
    });

})(jQuery);
