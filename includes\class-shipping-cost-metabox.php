<?php
/**
 * ميتا بوكس رسوم التوصيل الإضافية للمنتجات
 * 
 * يسمح بإضافة رسوم توصيل إضافية لكل منتج أو متغير بشكل فردي
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * كلاس ميتا بوكس رسوم التوصيل الإضافية
 */
class Form_Elrakami_Shipping_Cost_Metabox {

    /**
     * البناء
     */
    public function __construct() {
        // إضافة ميتا بوكس للمنتجات البسيطة
        add_action('woocommerce_product_options_shipping', array($this, 'add_shipping_cost_field'));
        add_action('woocommerce_process_product_meta', array($this, 'save_shipping_cost_field'));

        // إضافة حقول للمنتجات المتغيرة - بعد حقل إظهار شارة الكمية
        add_action('woocommerce_variation_options', array($this, 'add_variation_shipping_cost_field'), 10, 3);
        add_action('woocommerce_save_product_variation', array($this, 'save_variation_shipping_cost_field'), 10, 2);

        // إضافة أعمدة في قائمة المنتجات
        add_filter('manage_edit-product_columns', array($this, 'add_shipping_cost_column'));
        add_action('manage_product_posts_custom_column', array($this, 'display_shipping_cost_column'), 10, 2);

        // إضافة CSS و JavaScript للواجهة
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_styles'));
        add_action('admin_footer', array($this, 'add_admin_scripts'));

        // إضافة إشعار للمدراء حول الميزة الجديدة
        add_action('admin_notices', array($this, 'show_feature_notice'));
    }

    /**
     * إضافة حقل رسوم التوصيل الإضافية للمنتجات البسيطة
     */
    public function add_shipping_cost_field() {
        global $post;
        ?>
        <div class="options_group form-elrakami-shipping-cost-section">
            <h4 style="padding: 12px; margin: 0 0 15px 0; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; font-weight: 600; color: #23282d;">
                <span class="dashicons dashicons-cart" style="margin-left: 5px; color: #0073aa;"></span>
                رسوم التوصيل الإضافية - Form Elrakami
            </h4>

            <div style="padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin-bottom: 15px;">
                <p style="margin: 0; font-size: 13px; color: #856404;">
                    <strong>كيف تعمل:</strong> هذه الرسوم ستُضاف إلى تكلفة التوصيل الأساسية للولاية.<br>
                    <strong>مثال:</strong> إذا كانت تكلفة التوصيل 500 دج وأضفت 200 دج هنا، ستصبح التكلفة النهائية 700 دج.
                </p>
            </div>

            <?php
            // حقل رسوم التوصيل الإضافية
            woocommerce_wp_text_input(array(
                'id' => '_form_elrakami_additional_shipping_cost',
                'label' => 'رسوم التوصيل الإضافية (دج)',
                'placeholder' => '0',
                'desc_tip' => true,
                'description' => 'أدخل المبلغ الإضافي الذي سيُضاف لتكلفة التوصيل لهذا المنتج. اتركه فارغاً أو 0 إذا لم تكن هناك رسوم إضافية.',
                'type' => 'number',
                'custom_attributes' => array(
                    'step' => '1',
                    'min' => '0'
                ),
                'value' => get_post_meta($post->ID, '_form_elrakami_additional_shipping_cost', true)
            ));

            // حقل وصف الرسوم (اختياري)
            woocommerce_wp_textarea_input(array(
                'id' => '_form_elrakami_shipping_cost_description',
                'label' => 'وصف الرسوم الإضافية (اختياري)',
                'placeholder' => 'مثال: رسوم إضافية للمنتجات الثقيلة',
                'desc_tip' => true,
                'description' => 'وصف اختياري يوضح سبب الرسوم الإضافية. سيظهر للعميل في النموذج.',
                'rows' => 2,
                'value' => get_post_meta($post->ID, '_form_elrakami_shipping_cost_description', true)
            ));
            ?>
        </div>

        <style>
        .form-elrakami-shipping-cost-section {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            background: #fafafa;
        }
        .form-elrakami-shipping-cost-section .dashicons {
            font-size: 16px;
        }
        </style>
        <?php
    }

    /**
     * حفظ حقل رسوم التوصيل الإضافية للمنتجات البسيطة
     */
    public function save_shipping_cost_field($post_id) {
        // التحقق من الصلاحيات
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // حفظ رسوم التوصيل الإضافية
        if (isset($_POST['_form_elrakami_additional_shipping_cost'])) {
            $additional_cost = sanitize_text_field($_POST['_form_elrakami_additional_shipping_cost']);
            $additional_cost = floatval($additional_cost);
            
            if ($additional_cost >= 0) {
                update_post_meta($post_id, '_form_elrakami_additional_shipping_cost', $additional_cost);
            } else {
                delete_post_meta($post_id, '_form_elrakami_additional_shipping_cost');
            }
        }

        // حفظ وصف الرسوم
        if (isset($_POST['_form_elrakami_shipping_cost_description'])) {
            $description = sanitize_textarea_field($_POST['_form_elrakami_shipping_cost_description']);
            
            if (!empty($description)) {
                update_post_meta($post_id, '_form_elrakami_shipping_cost_description', $description);
            } else {
                delete_post_meta($post_id, '_form_elrakami_shipping_cost_description');
            }
        }
    }

    /**
     * إضافة حقل رسوم التوصيل الإضافية للمتغيرات
     */
    public function add_variation_shipping_cost_field($loop, $variation_data, $variation) {
        ?>
        <div class="form-elrakami-variation-shipping-section">
            <p class="form-row form-row-full">
                <strong style="display: block; margin-bottom: 10px; color: #23282d;">
                    <span class="dashicons dashicons-cart" style="margin-left: 5px;"></span>
                    رسوم التوصيل الإضافية - Form Elrakami
                </strong>
            </p>

            <p class="form-row form-row-first">
                <label for="_form_elrakami_additional_shipping_cost_<?php echo $loop; ?>">
                    رسوم التوصيل الإضافية (دج)
                    <span class="woocommerce-help-tip" data-tip="أدخل المبلغ الإضافي الذي سيُضاف لتكلفة التوصيل لهذا المتغير"></span>
                </label>
                <input type="number"
                       id="_form_elrakami_additional_shipping_cost_<?php echo $loop; ?>"
                       name="_form_elrakami_additional_shipping_cost[<?php echo $loop; ?>]"
                       value="<?php echo esc_attr(get_post_meta($variation->ID, '_form_elrakami_additional_shipping_cost', true)); ?>"
                       placeholder="0"
                       min="0"
                       step="1"
                       style="width: 100%;" />
            </p>

            <p class="form-row form-row-last">
                <label for="_form_elrakami_shipping_cost_description_<?php echo $loop; ?>">
                    وصف الرسوم (اختياري)
                    <span class="woocommerce-help-tip" data-tip="وصف اختياري يوضح سبب الرسوم الإضافية"></span>
                </label>
                <input type="text"
                       id="_form_elrakami_shipping_cost_description_<?php echo $loop; ?>"
                       name="_form_elrakami_shipping_cost_description[<?php echo $loop; ?>]"
                       value="<?php echo esc_attr(get_post_meta($variation->ID, '_form_elrakami_shipping_cost_description', true)); ?>"
                       placeholder="مثال: رسوم إضافية للحجم الكبير"
                       style="width: 100%;" />
            </p>

            <p class="form-row form-row-full" style="margin-bottom: 0;">
                <small style="color: #666; font-style: italic;">
                    <strong>ملاحظة:</strong> هذه الرسوم ستُضاف إلى تكلفة التوصيل الأساسية.
                    مثال: إذا كانت تكلفة التوصيل 500 دج وأضفت 200 دج هنا، ستصبح التكلفة النهائية 700 دج.
                </small>
            </p>
        </div>

        <style>
        .form-elrakami-variation-shipping-section {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .form-elrakami-variation-shipping-section .dashicons {
            color: #0073aa;
            font-size: 16px;
        }
        </style>
        <?php
    }

    /**
     * حفظ حقل رسوم التوصيل الإضافية للمتغيرات
     */
    public function save_variation_shipping_cost_field($variation_id, $loop) {
        // حفظ رسوم التوصيل الإضافية
        if (isset($_POST['_form_elrakami_additional_shipping_cost'][$loop])) {
            $additional_cost = sanitize_text_field($_POST['_form_elrakami_additional_shipping_cost'][$loop]);
            $additional_cost = floatval($additional_cost);
            
            if ($additional_cost >= 0) {
                update_post_meta($variation_id, '_form_elrakami_additional_shipping_cost', $additional_cost);
            } else {
                delete_post_meta($variation_id, '_form_elrakami_additional_shipping_cost');
            }
        }

        // حفظ وصف الرسوم
        if (isset($_POST['_form_elrakami_shipping_cost_description'][$loop])) {
            $description = sanitize_text_field($_POST['_form_elrakami_shipping_cost_description'][$loop]);
            
            if (!empty($description)) {
                update_post_meta($variation_id, '_form_elrakami_shipping_cost_description', $description);
            } else {
                delete_post_meta($variation_id, '_form_elrakami_shipping_cost_description');
            }
        }
    }

    /**
     * إضافة عمود رسوم التوصيل في قائمة المنتجات
     */
    public function add_shipping_cost_column($columns) {
        $new_columns = array();
        
        foreach ($columns as $key => $column) {
            $new_columns[$key] = $column;
            
            // إضافة العمود بعد عمود السعر
            if ($key === 'price') {
                $new_columns['shipping_cost'] = 'رسوم التوصيل الإضافية';
            }
        }
        
        return $new_columns;
    }

    /**
     * عرض رسوم التوصيل في عمود قائمة المنتجات
     */
    public function display_shipping_cost_column($column, $post_id) {
        if ($column === 'shipping_cost') {
            $additional_cost = get_post_meta($post_id, '_form_elrakami_additional_shipping_cost', true);
            
            if ($additional_cost && $additional_cost > 0) {
                echo '<span style="color: #d63638; font-weight: 600;">+' . number_format($additional_cost, 0) . ' دج</span>';
                
                $description = get_post_meta($post_id, '_form_elrakami_shipping_cost_description', true);
                if ($description) {
                    echo '<br><small style="color: #666;">' . esc_html($description) . '</small>';
                }
            } else {
                echo '<span style="color: #999;">—</span>';
            }
        }
    }

    /**
     * تحميل أنماط CSS للواجهة الإدارية
     */
    public function enqueue_admin_styles($hook) {
        global $post_type;

        if ($post_type === 'product' && in_array($hook, array('post.php', 'post-new.php'))) {
            wp_add_inline_style('woocommerce_admin_styles', '
                /* أنماط المنتجات البسيطة */
                .form-elrakami-shipping-cost-section {
                    border: 1px solid #ddd !important;
                    border-radius: 6px !important;
                    padding: 15px !important;
                    margin: 15px 0 !important;
                    background: #fafafa !important;
                }

                .form-elrakami-shipping-cost-section h4 {
                    display: flex;
                    align-items: center;
                    margin: 0 0 15px 0 !important;
                    padding: 12px !important;
                    background: #f8f9fa !important;
                    border: 1px solid #ddd !important;
                    border-radius: 4px !important;
                }

                .form-elrakami-shipping-cost-section .dashicons {
                    color: #0073aa;
                    font-size: 16px;
                    margin-left: 5px;
                }

                /* أنماط المتغيرات */
                .form-elrakami-variation-shipping-section {
                    background: #f9f9f9 !important;
                    border: 1px solid #ddd !important;
                    border-radius: 4px !important;
                    padding: 15px !important;
                    margin: 15px 0 !important;
                }

                .form-elrakami-variation-shipping-section strong {
                    display: flex;
                    align-items: center;
                    color: #23282d;
                    font-size: 14px;
                }

                .form-elrakami-variation-shipping-section .dashicons {
                    color: #0073aa;
                    font-size: 16px;
                    margin-left: 5px;
                }

                /* تحسينات عامة */
                .form-elrakami-shipping-cost-section .woocommerce-help-tip,
                .form-elrakami-variation-shipping-section .woocommerce-help-tip {
                    margin-right: 5px;
                }
            ');
        }
    }

    /**
     * الحصول على رسوم التوصيل الإضافية للمنتج
     */
    public static function get_additional_shipping_cost($product_id, $variation_id = 0) {
        // إذا كان هناك متغير، استخدم معرف المتغير
        $id = $variation_id > 0 ? $variation_id : $product_id;
        
        $additional_cost = get_post_meta($id, '_form_elrakami_additional_shipping_cost', true);
        return floatval($additional_cost);
    }

    /**
     * الحصول على وصف رسوم التوصيل الإضافية للمنتج
     */
    public static function get_additional_shipping_cost_description($product_id, $variation_id = 0) {
        // إذا كان هناك متغير، استخدم معرف المتغير
        $id = $variation_id > 0 ? $variation_id : $product_id;
        
        return get_post_meta($id, '_form_elrakami_shipping_cost_description', true);
    }

    /**
     * إضافة JavaScript للواجهة الإدارية
     */
    public function add_admin_scripts() {
        global $post_type;

        if ($post_type === 'product') {
            ?>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                // إضافة تأثيرات بصرية للحقول
                function highlightShippingCostFields() {
                    // تمييز الحقول عند التركيز
                    $('input[name*="_form_elrakami_additional_shipping_cost"]').on('focus', function() {
                        $(this).closest('.form-row, .options_group').addClass('form-elrakami-focused');
                    }).on('blur', function() {
                        $(this).closest('.form-row, .options_group').removeClass('form-elrakami-focused');
                    });

                    // تحديث المعاينة عند تغيير القيمة
                    $('input[name*="_form_elrakami_additional_shipping_cost"]').on('input', function() {
                        var value = parseFloat($(this).val()) || 0;
                        var preview = $(this).siblings('.shipping-cost-preview');

                        if (preview.length === 0) {
                            preview = $('<small class="shipping-cost-preview" style="color: #0073aa; font-weight: 600; display: block; margin-top: 5px;"></small>');
                            $(this).after(preview);
                        }

                        if (value > 0) {
                            preview.text('سيتم إضافة ' + value + ' دج لتكلفة التوصيل');
                        } else {
                            preview.text('');
                        }
                    });
                }

                // تطبيق التأثيرات عند تحميل الصفحة
                highlightShippingCostFields();

                // إعادة تطبيق التأثيرات عند إضافة متغيرات جديدة
                $(document).on('woocommerce_variations_loaded', function() {
                    highlightShippingCostFields();
                });

                // إضافة أنماط CSS إضافية
                $('<style>')
                    .prop('type', 'text/css')
                    .html(`
                        .form-elrakami-focused {
                            background-color: #f0f8ff !important;
                            border-color: #0073aa !important;
                            box-shadow: 0 0 5px rgba(0, 115, 170, 0.3) !important;
                        }

                        .shipping-cost-preview {
                            animation: fadeIn 0.3s ease-in;
                        }

                        @keyframes fadeIn {
                            from { opacity: 0; }
                            to { opacity: 1; }
                        }

                        .form-elrakami-shipping-cost-section,
                        .form-elrakami-variation-shipping-section {
                            transition: all 0.3s ease;
                        }

                        .form-elrakami-shipping-cost-section:hover,
                        .form-elrakami-variation-shipping-section:hover {
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }
                    `)
                    .appendTo('head');
            });
            </script>
            <?php
        }
    }

    /**
     * عرض إشعار للمدراء حول الميزة الجديدة
     */
    public function show_feature_notice() {
        global $post_type, $pagenow;

        // عرض الإشعار فقط في صفحات المنتجات
        if ($post_type === 'product' && in_array($pagenow, array('edit.php', 'post.php', 'post-new.php'))) {
            // التحقق من أن الإشعار لم يتم إخفاؤه
            if (!get_user_meta(get_current_user_id(), 'form_elrakami_shipping_cost_notice_dismissed', true)) {
                ?>
                <div class="notice notice-info is-dismissible" id="form-elrakami-shipping-cost-notice">
                    <h3 style="margin-top: 0;">🚀 ميزة جديدة: رسوم التوصيل الإضافية!</h3>
                    <p>يمكنك الآن إضافة رسوم توصيل إضافية لأي منتج مباشرة من صفحة تحرير المنتج:</p>
                    <ul style="margin-right: 20px;">
                        <li><strong>للمنتجات البسيطة:</strong> ابحث عن قسم "رسوم التوصيل الإضافية" في تبويب الشحن</li>
                        <li><strong>للمنتجات المتغيرة:</strong> ستجد الحقول في كل متغير منفصل</li>
                        <li><strong>مثال:</strong> إذا كانت تكلفة التوصيل 500 دج وأضفت 200 دج، ستصبح التكلفة 700 دج</li>
                    </ul>
                    <p>
                        <button type="button" class="button button-primary" onclick="jQuery('#form-elrakami-shipping-cost-notice').fadeOut(); jQuery.post(ajaxurl, {action: 'form_elrakami_dismiss_shipping_cost_notice', _ajax_nonce: '<?php echo wp_create_nonce('form_elrakami_dismiss_notice'); ?>'});">
                            فهمت، لا تظهر هذا مرة أخرى
                        </button>
                    </p>
                </div>
                <?php
            }
        }
    }
}

// معالج AJAX لإخفاء الإشعار
add_action('wp_ajax_form_elrakami_dismiss_shipping_cost_notice', function() {
    check_ajax_referer('form_elrakami_dismiss_notice', '_ajax_nonce');

    if (current_user_can('manage_options')) {
        update_user_meta(get_current_user_id(), 'form_elrakami_shipping_cost_notice_dismissed', true);
        wp_send_json_success();
    } else {
        wp_send_json_error('غير مصرح');
    }
});

// تهيئة الكلاس
new Form_Elrakami_Shipping_Cost_Metabox();
