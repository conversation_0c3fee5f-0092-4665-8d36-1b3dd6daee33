<?php
/**
 * بيانات البلديات الجزائرية - نسخة محسنة
 *
 * هذا الملف يحتوي على بيانات البلديات الجزائرية مصنفة حسب الولاية
 * تم تحسين البنية لتقليل التكرار وتحسين الأداء
 */

// تجنب الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * بيانات الولايات والبلديات الجزائرية - مصدر البيانات الوحيد
 *
 * @return array مصفوفة بالولايات والبلديات
 */
function form_elrakami_get_algeria_data() {
    static $algeria_data = null;

    // استخدام static للتخزين المؤقت في الذاكرة
    if ($algeria_data !== null) {
        return $algeria_data;
    }

    $algeria_data = array(
        "01: ولاية أدرار" => array("أدرار", "تامست", "شروين", "رقان", "إن زغمير", "تيت", "قصر قدور", "تسابيت", "تيميمون", "أولاد السعيد", "زاوية كونتة", "أولف", "تيمقتن", "تامنطيت", "فنوغيل", "تنركوك", "دلدول", "سالي", "أقبلي", "المطارفة", "أولاد أحمد تيمي", "بودة", "أوقروت", "طالمين", "برج باجي مختار", "السبع"),

        "02: ولاية الشلف" => array("الشلف", "تنس", "بنايرية", "الكريمية", "تاجنة", "تاوقريت", "بني حواء", "صبحة", "حرشون", "أولاد فارس", "سيدي عكاشة", "بوقادير", "بني راشد", "تلعصة", "الهرنفة", "وادى قوسين", "الظهرة", "أولاد عباس", "السنجاس", "الزبوجة", "وادي سلي", "أبو الحسن", "المرسى", "الشطية", "سيدي عبد الرحمن", "موزاية", "وادي الفضة", "عين مران", "تاشتة", "حجاج", "أم الدروع", "بوزغاية", "سوق الحد", "بريرة"),

        "03: ولاية الأغواط" => array("الأغواط", "قصر الحيران", "بن ناصر بن شهرة", "سيدي مخلوف", "حاسي الدلاعة", "حاسي الرمل", "عين ماضي", "تاجموت", "الخنق", "قلتة سيدي سعد", "عين سيدي علي", "البيضاء", "بريدة", "الغيشة", "الحاج المشري", "سبقاق", "تاويالة", "تاجرونة", "أفلو", "العسافية", "وادي مرة", "وادي مزي"),

        "04: ولاية أم البواقي" => array("أم البواقي", "عين البيضاء", "عين مليلة", "بحير شرقي", "العامرية", "سيقوس", "البلالة", "عين بابوش", "بريش", "أولاد حملة", "الضلعة", "عين كرشة", "هنشير تومغني", "الجازية", "عين الديس", "فكرينة", "سوق نعمان", "الزرق", "الفجوج بوغرارة سعودي", "أولاد زواي", "بئر الشهداء", "المشيرة", "قصر صباحي", "الراحية", "الزيتون", "وادي نيني", "بوغرارة النص", "عين ببوش"),

        "05: ولاية باتنة" => array("باتنة", "غسيرة", "معافة", "مروانة", "سريانة", "منعة", "المعذر", "تازولت", "نقاوس", "القيقبة", "إينوغيسن", "عيون العصافير", "جرمة", "بيطام", "عزيل عبد القادر", "آريس", "كيمل", "تيلاطو", "عين جاسر", "أولاد سلام", "تيغرغار", "عين ياقوت", "سفيان", "فسديس", "الرحبات", "تيغانمين", "لمسان", "زانة البيضاء", "شير", "أولاد فاضل", "تاكسلانت", "القصبات", "أولاد عوف", "بولهيلات", "لارباع", "آمدوكال", "بومية", "بومقر", "بيطام", "تيمقاد", "أشمول", "فم الطوب", "وادي الشعبة", "تالخمت", "وادي الماء", "تاغرغايت", "ثنية العابد", "وادي الطاقة", "أولاد يعقوب", "بوزينة", "الجزار", "تاكوت", "عين التوتة", "حيدوسة", "تيفلفال", "رأس العيون"),

        "06: ولاية بجاية" => array("بجاية", "بوخليفة", "تيشي", "تالة حمزة", "أميزور", "تامريجت", "أوقاس", "آيت سماعيل", "تاسكريوت", "تمقرة", "آغرام", "كسيلة", "تاوريرت آغيل", "أمالو", "بوحمزة", "كنديرة", "خراطة", "فرعون", "بني جليل", "القصر", "توجة", "بوجليل", "بني مليكش", "سوق أوفلة", "شميني", "تيفرة", "تينبذار", "سيدي عياد", "تيمزريت", "مالبو", "أوزلاقن", "أيت رزين", "شلاطة", "أقبو", "إيغرم", "آيت اسماعيل", "درقينة", "برباشة", "سمعون", "أكفادو", "لفلاي", "شميني", "سوق لخميس"),

        "07: ولاية بسكرة" => array("بسكرة", "ليوة", "لوطاية", "جمورة", "البرانيس", "القنطرة", "عين زعطوط", "سيدي عقبة", "شتمة", "تكوت", "الحوش", "عين الناقة", "زربية الوادي", "المزيرعة", "خنقة سيدي ناجي", "الفيض", "مشونش", "طولقة", "بوشقرون", "برج بن عزوز", "ليشانه", "فوغاله", "الغروس", "أولاد جلال", "الدوسن", "الشعيبة", "سيدي خالد", "مخادمة", "أوماش", "ليوة", "مليلي", "أورلال", "امليلي"),

        "08: ولاية بشار" => array("بشار", "أرزن", "قصابي", "لحمر", "بني ونيف", "مريجة", "تبلبالة", "تاغيت", "المريجة", "موغل", "بني عباس", "إقلي", "تيمودي", "كرزاز", "أولاد خضير", "تامترت", "بني يخلف"),

        "09: ولاية البليدة" => array("البليدة", "الشبلي", "بوعرفة", "وادي العلايق", "أولاد يعيش", "الشريعة", "العفرون", "الشفة", "حمام ملوان", "بن خليل", "الصومعة", "موزاية", "مفتاح", "أولاد سلامة", "بوعينان", "الأربعاء", "جبابرة", "بوقرة", "قرواو", "بوفاريك", "صوحان", "الأخضرية", "بني مراد", "بني تامو", "بوعينان"),

        "10: ولاية البويرة" => array("البويرة", "الأخضرية", "سور الغزلان", "مشد الله", "الهاشمية", "أهل القصر", "عين بسام", "بشلول", "الأسنام", "أغبالو", "تاغزوت", "رأس الوادي", "الشرفة", "سوق الخميس", "القادرية", "حنيف", "حيزر", "الدشمية", "الأخضرية", "معالة", "العجيبة", "آث منصور", "برج أوخريص", "الحاكمية", "عمر", "عين العلوي", "ديرة", "الخبوزية", "أولاد راشد", "بوكرم", "أعمر", "تاقربوست", "ريدان", "جباحية", "آيت لعزيز", "آث الزيري"),

        "11: ولاية تمنراست" => array("تمنراست", "أبلسة", "إدلس", "تازروق", "عين امقل", "عين صالح", "فقارة الزوى", "إينغر"),

        "12: ولاية تبسة" => array("تبسة", "بئر العاتر", "الشريعة", "الونزة", "العوينات", "الكويف", "مرسط", "نقرين", "بئر مقدم", "العقلة", "الماء الأبيض", "أم علي", "صفصاف الوسرى", "الحويجبات", "بكارية", "بولحاف الدير", "قوريقر", "بئر الذهب", "الحمامات", "ثليجان", "عين الزرقاء", "المريج", "بوخضرة", "الونزة", "المزرعة", "قريقر", "بئر الذهب", "الحمامات", "الكويف", "العقلة المالحة"),

        "13: ولاية تلمسان" => array("تلمسان", "باب العسة", "دار يغمراسن", "الرمشي", "الغزوات", "ندرومة", "شتوان", "مغنية", "بني بوسعيد", "مرسى بن مهيدي", "فلاوسن", "عزايل", "صبرة", "الغور", "وادي الشولي", "عين فزة", "أولاد ميمون", "عين يوسف", "زناتة", "بني سنوس", "باب العسة", "دار يغمراسن", "فلاوسن", "عزايل", "السبعة شيوخ", "تيرني بني هديل", "أولاد رياح", "سوق الثلاثاء", "سيدي العبدلي", "المشرية", "البويهي", "حنين", "سبدو", "تيرني", "بني مستر", "بني صميل", "سيدي الجيلالي", "جبالة", "القور", "وادي الشولي", "عين نحالة", "عين فتاح", "تيان", "أولاد مهدي", "سبعة شيوخ", "سيدي مجاهد", "بن سكران", "حناية", "سواني", "سيدي ورياش", "العريشة", "سوق الثلاثاء"),

        "14: ولاية تيارت" => array("تيارت", "مشرع الصفا", "عين بوشقيف", "مدروسة", "سيدي علي ملال", "عين الذهب", "السبت", "مغيلة", "سي عبد الغني", "دحموني", "رحوية", "مهدية", "سوقر", "عين الحديد", "قصر الشلالة", "فرندة", "عين كرمس", "قرطوفة", "سيدي حسني", "جبيلات الرصفاء", "نعمة", "مدريسة", "زمالة الأمير عبد القادر", "مادنة", "تاخمرت", "سيدي بختي", "عين دزاريت", "سبعين", "الناظورة", "تاقدمت", "الرشايقة", "شحيمة", "أولاد جرادة", "الساقية", "الفايجة", "العيون", "سيدي عبد الرحمن", "سرغين", "بوقرة", "حمادية", "الرحوية", "مدريسة", "مشرع صفا"),

        "15: ولاية تيزي وزو" => array("تيزي وزو", "عين الحمام", "أقبو", "عزازقة", "بوغني", "درع بن خدة", "فريحة", "إليلتن", "إفرحونن", "إرجن", "مقلع", "تيميزار", "ماكودة", "تيقزيرت", "واضية", "أزفون", "إعكورن", "الأربعاء نايت إيراثن", "بني دوالة", "بني يني", "بوزقن", "إيلولة أومالو", "إيفيغاء", "أيت شفعة", "فريقات", "سيدي نعمان", "إبودرارن", "أغريب", "إيجر", "إيلولا أومالو", "إيملولا", "أيت يحيى", "أيت محمود", "أيت خلفون", "أيت أومالو", "أقرو", "أسي يوسف", "أزمور إيغيل", "بني زيكي", "بني زمنزر", "بوغني", "بوجيمة", "بوحينون", "بونوح", "مشطراس", "ميزرانة", "واسيف", "ذراع الميزان", "ذراع بن خدة", "تيرمتين", "تيزي نتلاثة", "تيزي غنيف", "تيزي نرجة", "زكري", "واد قسي", "يعقورن", "تاورقة", "ميزرانة", "معكودة", "واسيف", "واضية", "أزفون", "أيت عيسى ميمون", "أيت أغواشة", "أيت بوعادو", "أيت تودرت", "عين الزاوية", "صوامع", "أقني قغران"),

        "16: ولاية الجزائر" => array("الجزائر الوسطى", "سيدي امحمد", "حيدرة", "المرادية", "باب الوادي", "بولوغين", "القصبة", "وادي قريش", "بئر مراد رايس", "الأبيار", "بوزريعة", "بئر خادم", "الحراش", "براقي", "وادي السمار", "باش جراح", "حسين داي", "القبة", "دار البيضاء", "باب الزوار", "بن عكنون", "دالي إبراهيم", "الحمامات", "المغارية", "المدنية", "محمدية", "برج الكيفان", "بني مسوس", "الكاليتوس", "وادي شعيب", "جسر قسنطينة", "الدرارية", "دوالة", "بوروبة", "الرغاية", "عين طاية", "برج البحري", "مرسى الحجاج", "تسالة المرجة", "أولاد فايت", "الرحمانية", "سويدانية", "المعالمة", "الشراربة", "أولاد شبل", "سيدي موسى", "عين البنيان", "زرالدة", "الشراقة", "أولاد فايت", "ستاوالي", "الرايس حميدو", "العاشور", "خرايسية", "الدويرة", "أولاد شبل"),

        "17: ولاية الجلفة" => array("الجلفة", "حاسي بحبح", "عين وسارة", "دار الشيوخ", "حد الصحاري", "فيض البطمة", "بيرين", "الإدريسية", "مسعد", "القديد", "سيدي لعجال", "حاسي فدول", "عين الإبل", "عين معبد", "تاعظميت", "دلدول", "الخميس", "سد رحال", "أم العظام", "عين الشهداء", "بن يعقوب", "زكار", "دويس", "حاسي العش", "مليليحة", "سلمانة", "عمورة", "بويرة الأحداب", "الشارف", "بني يعقوب", "زعفران", "القرطوفة", "سيدي بايزيد", "مجبارة", "قطارة", "عين الشهداء", "عين افقه"),

        "18: ولاية جيجل" => array("جيجل", "الطاهير", "الشقفة", "الميلية", "سيدي معروف", "السطارة", "العوانة", "زيامة منصورية", "الأمير عبد القادر", "الشحنة", "أولاد يحيى خدروش", "غبالة", "بوراوي بلهادف", "جيملة", "سلمى بن زيادة", "بوسيف أولاد عسكر", "القنار نشفي", "أولاد رابح", "الجمعة بني حبيبي", "برج الطهر", "خيري واد عجول", "تاكسنة", "قاوس", "سيدي عبد العزيز", "وجانة", "سطارة", "الطاهير", "الميلية", "تاكسنة", "قاوس", "جيملة", "بني فودة", "وجانة"),

        "19: ولاية سطيف" => array("سطيف", "عين أرنات", "عين عباسة", "عين أزال", "عين الكبيرة", "عين القراج", "عين لقراج", "عين الروى", "عين السبت", "عين ولمان", "عموشة", "أيت تيزي", "أيت نوال مزادة", "عين الحجر", "بابور", "بازر سكرة", "بني عزيز", "بني شبانة", "بني فودة", "بني ورتيلان", "بئر العرش", "بئر حدادة", "بوعنداس", "بوقاعة", "بوسلام", "بوطالب", "الدهامشة", "جميلة", "ذراع قبيلة", "قنزات", "قلال", "قلتة زرقاء", "قصر الأبطال", "الحامة", "الحساسنة", "الأوريسية", "مزلوق", "واد البارد", "أولاد عدوان", "أولاد صابر", "أولاد سي أحمد", "أولاد تبان", "الرصفة", "صالح باي", "سرج الغول", "سطيف", "تاشودة", "تيزي نبشار", "تلة", "حمام السخنة", "حمام قرقور", "حربيل", "ماوكلان", "صالح باي", "سرج الغول", "تاشودة", "تيزي نبشار", "تالة إيفاسن", "تيزي نبشار", "تلة"),

        "20: ولاية السعيدة" => array("السعيدة", "دوي ثابت", "عين الحجر", "سيدي أحمد", "سيدي بوبكر", "الحساسنة", "معمورة", "يوب", "أولاد خالد", "مولاي العربي", "عين السخونة", "سيدي عمر", "تيرسين", "عين السلطان", "أولاد إبراهيم"),

        "21: ولاية سكيكدة" => array("سكيكدة", "الحدائق", "عزابة", "بن عزوز", "القل", "الحروش", "سيدي مزغيش", "تمالوس", "عين قشرة", "أم الطوب", "بني ولبان", "الولجة بولبلوط", "بين الويدان", "الشرايع", "وادي الزهور", "الزيتونة", "الكركرة", "صالح بوالشعور", "رمضان جمال", "فلفلة", "قنواع", "الغدير", "بوشطاطة", "عين زويت", "بني زيد", "الحدائق", "عزابة", "جندل", "القل", "وادى الزهور", "الحروش", "زردازة", "أولاد حبابة", "أولاد أحباب", "حمادي كرومة", "كنوية", "بني بشير", "عين بوزيان", "عين شرشار", "السبة", "القل", "زيتونة", "الزهور", "الحروش"),

        "22: ولاية سيدي بلعباس" => array("سيدي بلعباس", "تسالة", "سيدي إبراهيم", "مصطفى بن إبراهيم", "تلاغ", "مرين", "رأس الماء", "عين تندمين", "سفيزف", "الحصيبة", "عين البرد", "سيدي علي بوسيدي", "بضرابين المقراني", "سيدي حمادوش", "عين عدان", "مكدرة", "تنيرة", "مولاي سليسن", "الطابية", "بوخنفيس", "سيدي علي بن يوب", "بضرابين المقراني", "وادي السبع", "مرحوم", "سيدي حمادوش", "عين عدان", "زروالة", "بن بدي الهواري", "سيدي خالد", "سيدي يعقوب", "طابية", "تاوريرة", "تفسور", "تيغاليمات", "تلموني", "تنيرة", "تسالة", "وادي تاوريرة", "وادي سفيون", "حاسي زهانة", "حاسي دحو", "لمطار", "مزاورو", "مسيد", "سيدي شعيب", "سيدي دحو", "سيدي حمادوش", "سيدي خالد", "سيدي لحسن", "سيدي يعقوب", "تاودمكة", "تاوريرة", "تفسور", "تلاغ", "تلموني", "تنيرة", "زروالة"),

        "23: ولاية عنابة" => array("عنابة", "برحال", "الحجار", "العلمة", "البوني", "واد العنب", "الشرفة", "عين الباردة", "شطايبي", "سيدي عمار", "تريعات", "برحال"),

        "24: ولاية قالمة" => array("قالمة", "وادي الزناتي", "تاملوكة", "وادي الشحم", "عين مخلوف", "عين بن بيضاء", "بوشقوف", "هيليوبوليس", "قلعة بوصبع", "حمام دباغ", "الركنية", "جبالة الخميسي", "بني مزلين", "بوحمدان", "عين حساينية", "الدهوارة", "سلاوة عنونة", "النشماية", "بوعاتي محمود", "خزارة", "بن جراح", "بومهرة أحمد", "عين صندل", "عين مخلوف", "عين بن بيضاء", "بئر الشهداء", "بوحشانة", "بومهرة أحمد", "الدواودة", "الفجوج", "هواري بومدين", "حمام النبائل", "شحانية", "لخزارة", "المجاز الصفاء", "مجاز الصفاء", "نشماية", "وادي فراغة", "وادي الشحم", "وادي الزناتي", "عين العربي", "عين بن بيضاء", "برج صباط", "براج الطهر", "بوحمدان"),

        "25: ولاية قسنطينة" => array("قسنطينة", "حامة بوزيان", "ابن باديس", "زيغود يوسف", "ديدوش مراد", "الخروب", "عين عبيد", "بني حميدان", "أولاد رحمون", "عين السمارة", "مسعود بوجريو", "ابن زياد"),

        "26: ولاية المدية" => array("المدية", "عين بوسيف", "العيساوية", "أولاد عنتر", "وزرة", "تابلاط", "العزيزية", "السواقي", "قصر البخاري", "العمارية", "وادي حربيل", "الثنية", "سيدي نعمان", "أولاد براهيم", "بني سليمان", "سيدي زيان", "تفراوت", "شلالة العذاورة", "بوغزول", "ذراع السمار", "عزيز", "أولاد هلال", "تلاوي", "باب الجزائر", "العزيزية", "واد حربيل", "عين بوسيف", "عين القصير", "عين الله", "أولاد انايل", "بن شكاو", "البواعيش", "بني سليمان", "البرواقية", "بوعيش", "بوشراحيل", "بوسكن", "بوغار", "بوشية", "شلالة العذاورة", "الشهبونية", "دميات", "دراق", "العوينات", "حناشة", "الحمدانية", "الكاف الأخضر", "مديانة", "مغراوة", "أولاد انايل", "أولاد هلال", "أولاد دايد", "وامري", "وادي حربيل", "مزغنة", "قصر البخاري", "الربعية", "سانق", "سدراية", "سغوان", "سي المحجوب", "سيدي الربيع", "سيدي زهار", "سيدي دامد", "السواقي", "وزرة", "تبلاط", "تافراوت", "ثلاث دوائر", "زبيرية", "زوبيرية"),

        "27: ولاية مستغانم" => array("مستغانم", "عين النويصي", "حاسي ماماش", "عين تادلس", "خير الدين", "سيدي علي", "سيدي لخضر", "عشعاشة", "بوقيراط", "ماسرة", "عين بودينار", "سيدي بلعطار", "خضرة", "عين سيدي شريف", "سيرات", "فرناكة", "أولاد بوغالم", "الصور", "الطواهرية", "نكمارية", "السوافلية", "منصورة", "ستيدية", "سيدي علي", "سوق الأربعاء", "سوق الثلاثاء", "صفصاف", "صيادة", "تازقايت", "السوافلية"),

        "28: ولاية المسيلة" => array("المسيلة", "أحمد راشدي", "حمام الضلعة", "أولاد دراج", "مقرة", "بلعايبة", "برهوم", "عين الحجل", "سيدي عيسى", "عين الملح", "مدية", "الخبانة", "شلال", "أولاد عدي لقبالة", "المعاضيد", "بن سرور", "أولاد سليمان", "الهامل", "بوسعادة", "ولتام", "بني يلمان", "سيدي هجرس", "وانوغة", "زرزور", "محمد بوضياف", "أولاد ماضي", "تامسة", "العش", "السليم", "تارمونت", "عين الريش", "سليم", "أولاد سيدي ابراهيم", "ونوغة", "زرزور", "بن زوه"),

        "29: ولاية معسكر" => array("معسكر", "بوحنفية", "هاشم", "غريس", "المأمونية", "المحمدية", "واد الأبطال", "العلايمية", "سيدي عبد المومن", "سيدي عبد الجبار", "عين فكان", "البرج", "بني شقران", "القعدة", "فروحة", "الغريس", "القرط", "الهاشم", "مكرانة", "ماقضة", "غروس", "سيدي دحو", "زهانة", "سجرارة", "الفحول", "الفراقيق", "سيدي قادة", "زلامة", "النمارية", "سيدي بوسعيد", "سيدي عبد الجبار", "سيق", "جرجرة", "تيغنيف", "وادي التاغية", "وادي الأبطال", "وادي العلايمية", "زهانة"),

        "30: ولاية ورقلة" => array("ورقلة", "أنقوسة", "حاسي مسعود", "الرويسات", "نقوسة", "الحجيرة", "تبسبست", "تماسين", "تقرت", "سيدي سليمان", "المقارين", "المنقر", "وادي العلندة", "البرمة", "بلدة عمر", "العالية", "تبسبست", "النزلة", "حاسي مسعود", "العالية", "المقارين", "تماسين", "تقرت"),

        "31: ولاية وهران" => array("وهران", "بطيوة", "مرسى الحجاج", "عين الترك", "قديل", "السانية", "وادي تليلات", "العنصر", "عين البية", "أرزيو", "سيدي الشحمي", "بئر الجير", "حاسي مفسوخ", "حاسي بن عقبة", "بن فريحة", "بوتليليس", "مسرغين", "بوفاتيس", "الكرمة", "طفراوي", "عين الكرمة", "السانية", "سيدي الشحمي", "سيدي بن يبقى", "البراية", "المرسى", "بئر الجير", "البراية", "بن فريحة", "بوتليليس", "بوسفر", "مسرغين", "وادي تليلات"),

        "32: ولاية البيض" => array("البيض", "بريزينة", "بوعلام", "ستيتن", "الأبيض سيد الشيخ", "بوسمغون", "غاسول", "بوقطب", "الخيثر", "الكراكدة", "البنود", "الشقيق", "توسمولين", "سيدي عمر", "سيدي سليمان", "اربوات", "شلالة", "كراكدة"),

        "33: ولاية إليزي" => array("إليزي", "جانت", "الدبداب", "برج عمر إدريس", "برج الحواس", "عين اميناس"),

        "34: ولاية برج بوعريريج" => array("برج بوعريريج", "رأس الوادي", "برج زمورة", "المنصورة", "المهير", "بن داود", "عين تاغروت", "عين تسرة", "سيدي مبارك", "الحمادية", "الياشير", "برج غدير", "جعافرة", "تقلعيت", "ثنية النصر", "العناصر", "خليل", "حسناوة", "العش", "القصور", "الماين", "تسامرت", "تيكستار", "تفرق", "غيلاسة", "حرازة", "اليشير", "القلة", "أولاد براهم", "أولاد دحمان", "أولاد سيدي إبراهيم", "رابطة", "تسامرت", "تيكستار"),

        "35: ولاية بومرداس" => array("بومرداس", "بودواو", "أولاد هداج", "باغلية", "بني عمران", "تيجلابين", "بغلية", "الثنية", "دلس", "سيدي داود", "البرواقية", "يسر", "زموري", "برج منايل", "قورصو", "تاورقة", "أعفير", "أولاد موسى", "شعبة العامر", "قدارة", "بن شود", "سوق الحد", "يسر", "الناصرية", "شعبة العامر", "سي مصطفى", "أولاد عيسى", "لقاطة", "قورصو", "حمادي", "لاربعطاش", "أولاد موسى", "تيمزريت", "برج منايل", "بني عمران", "تاورقة"),

        "36: ولاية الطارف" => array("الطارف", "البسباس", "بن مهيدي", "بوقوس", "القالة", "العيون", "بوحجار", "بريحان", "الذرعان", "شبيطة مختار", "الشط", "الشيحاني", "دريان", "بحيرة الطيور", "السوارخ", "بوثلجة", "حمام بني صالح", "عين العسل", "بوقوس", "بوحجار", "عصفور", "زريزر", "الزيتونة", "العيون", "عين الكرمة", "بريحان", "واد الزيتون", "الزيتونة", "بوقوس"),

        "37: ولاية تندوف" => array("تندوف", "أم العسل"),

        "38: ولاية تيسمسيلت" => array("تيسمسيلت", "الأزهرية", "بني حواء", "بني لحسن", "بني مايدة", "برج الأمير عبد القادر", "بويرة الأحداب", "اليوسفية", "الأرباع", "خميستي", "العيون", "العمارية", "الأحلاف", "المعاصم", "الملعب", "خميستي", "سيدي عابد", "سيدي بوتوشنت", "سيدي العنتري", "سيدي سليمان", "اليوسفية", "تملاحت", "تياسارت", "العيون", "العمارية", "لرجام"),

        "39: ولاية الوادي" => array("الوادي", "وادي العلندة", "الدبيلة", "قمار", "كوينين", "الرقيبة", "الرباح", "النخلة", "المقرن", "حساني عبد الكريم", "تغزوت", "ورماس", "البياضة", "العقلة", "سيدي عون", "الطريفاوي", "دوار الماء", "حاسي خليفة", "الطالب العربي", "بن قشة", "المقرن", "اميه ونسة", "حاسي خليفة", "قمار", "تغزوت", "ورماس", "ميه ونسة", "سطيل", "المغير", "جامعة", "تندلة"),

        "40: ولاية خنشلة" => array("خنشلة", "قايس", "طامزة", "تاوزيانت", "بابار", "أنسيغة", "الحامة", "عين الطويلة", "متوسة", "ششار", "أولاد رشاش", "بوحمامة", "الرميلة", "المحمل", "يابوس", "الولجة", "الحامة", "شلية", "الطويلة", "أولاد رشاش", "واد الطاقة", "بوحمامة", "يابوس", "طامزة", "تاوزيانت", "بغاي", "قايس", "ششار", "جلال", "خيران", "المحمل", "مصارة", "انسيغة"),

        "41: ولاية سوق أهراس" => array("سوق أهراس", "سدراتة", "أم العظائم", "تاورة", "الحدادة", "المراهنة", "أولاد مومن", "بئر بوحوش", "مداوروش", "أم العظائم", "تاورة", "الزوابي", "سيدي فرج", "سافل الويدان", "الراقوبة", "خميسة", "واد الكبريت", "ترقالت", "الزعرورية", "عين الزانة", "عين سلطان", "الحدادة", "الدريعة", "المشروحة", "أولاد إدريس", "الخضارة", "ويلان", "واتة", "الزوابي", "تيفاش", "المراهنة", "أولاد مومن", "وادي الكبريت", "الحنانشة", "سدراتة", "الأوريسية", "أولاد دريس", "المشروحة", "سيدي فرج", "تفاش", "زنابة", "تاورة", "ترقالت"),

        "42: ولاية تيبازة" => array("تيبازة", "الناظور", "الداموس", "الأرهاط", "حجوط", "سيدي عمر", "قوراية", "مناصر", "شرشال", "مراد", "سيدي غيلاس", "عين تاقورايت", "بواسماعيل", "حمر العين", "بوهارون", "سيدي راشد", "فوكة", "الدواودة", "مسلمون", "خميستي", "عين تاقورايت", "الداموس", "بني ميلك", "بورقيقة", "شعيبة", "دواودة", "القليعة", "حطاطبة", "لاحطاطة", "مناصر", "مراد", "الناظور", "سيدي سميان", "السواقي", "تيبازة", "حجوط"),

        "43: ولاية ميلة" => array("ميلة", "فرجيوة", "سيدي مروان", "القرارم قوقة", "تاجنانت", "وادي النجاء", "التلاغمة", "واد العثمانية", "الشيقارة", "ترعى باينان", "الميلية", "عين ملوك", "عميرة آراس", "تسدان حدادة", "تيبرقنت", "بوحاتم", "حمالة", "عين التين", "الرواشد", "تسالة لمطاعي", "مينار زارزة", "المشيرة", "سيدي خليفة", "زغاية", "دراحي بوصلاح", "أحمد راشدي", "اعميرة آراس", "سيدي مروان", "الشيقارة", "الميلية", "بوحاتم", "أولاد خلوف", "تاجنانت", "يحيى بني قشة", "ترعي باينان", "زغاية"),

        "44: ولاية عين الدفلى" => array("عين الدفلى", "مليانة", "بومدفع", "خميس مليانة", "حمام ريغة", "العامرة", "بوراشد", "العطاف", "المخاطرية", "عريب", "جليدة", "الحسينية", "الطارف", "عين السلطان", "واد الشرفاء", "عين البنيان", "واد الجمعة", "الروينة", "زدين", "عين الأشياخ", "طارق بن زياد", "عين تركي", "بربوش", "بطحية", "بومدفع", "العبادية", "بن علال", "بئر ولد خليفة", "بوراشد", "جندل", "جليدة", "الحسنية", "الحسينية", "العامرة", "العطاف", "عين بويحيى", "عين السلطان", "عين التركي", "العبادية"),

        "45: ولاية النعامة" => array("النعامة", "عين الصفراء", "تيوت", "سفيسيفة", "مغرار", "عسلة", "جنين بورزق", "القصدير", "مكمن بن عمار", "عين ورقة", "البيوض", "تيوت", "المشرية", "مغرار", "عسلة", "جنين بورزق", "مكمن بن عمار", "القصدير"),

        "46: ولاية عين تموشنت" => array("عين تموشنت", "عين الكيحل", "عقب الليل", "عين التلس", "الأمير عبد القادر", "حمام بوحجر", "بني صاف", "المالح", "شعبة اللحم", "العامرية", "حاسي الغلة", "أولاد الكيحل", "واد برقش", "أولاد بوجمعة", "وادي الصباح", "آغلال", "تامزوغة", "سيدي صافي", "أولاد خالد", "سيدي الصافي", "سيدي ورياش", "سيدي بومدين"),

        "47: ولاية غرداية" => array("غرداية", "ضاية بن ضحوة", "بريان", "متليلي", "القرارة", "بونورة", "العطف", "زلفانة", "سبسب", "المنصورة", "حاسي القارة", "حاسي لفحل", "المنيعة", "تمنطيط"),

        "48: ولاية غليزان" => array("غليزان", "وادي رهيو", "بلعسل بوزقزة", "سيدي محمد بن علي", "سيدي خطاب", "الحمادنة", "مديونة", "زمورة", "جديوية", "الرمكة", "واريزان", "مازونة", "القلعة", "سيدي سعادة", "عين الرحمة", "بن داود", "حد الشكالة", "بلعسل", "المطمر", "سيدي لزرق", "عمي موسى", "ولاد سيدي ميهوب", "واد السلام", "أولاد يعيش", "عين طارق", "حمري", "مرجة سيدي عابد", "بني درقن", "القطار", "منداس", "سيدي أمحمد بن عودة", "يلل", "سوق الحد", "دار بن عبد الله", "ولد سيدي الميهوب", "واد السلام", "وادي رهيو", "الرمقة", "واريزان", "مازونة", "بني زنطيس", "سيدي خطاب", "القلعة", "مديونة", "الولجة", "سيدي سعادة", "عين الرحمة", "عين طارق", "حمري", "أولاد سيدي الميهوب", "القطار", "مرجة سيدي عابد", "بني درقن", "منداس", "حد الشكالة", "بلعسل", "المطمر", "سيدي لزرق", "سيدي محمد بن علي", "الحمادنة", "سيدي محمد بن عودة", "يلل", "سوق الحد", "دار بن عبد الله", "جديوية", "زمورة"),

        "49: ولاية تيميمون" => array("تيميمون", "أوقروت", "تينركوك", "قصر قدور", "شروين", "طالمين", "أولاد سعيد", "المطارفة", "دلدول", "أولاد عيسى"),

        "50: ولاية برج باجي مختار" => array("برج باجي مختار"),

        "51: ولاية أولاد جلال" => array("أولاد جلال", "سيدي خالد", "شعيبة", "الدوسن", "البسباس", "رأس الميعاد"),

        "52: ولاية بني عباس" => array("بني عباس", "كرزاز", "تمودي", "بني يخلف", "كسير", "تمترت", "قصابي", "تبلبالة", "الواتة", "أولاد خضير", "تامترت", "إقلي"),

        "53: ولاية عين صالح" => array("عين صالح", "إينغر", "فقارة الزاوية"),

        "54: ولاية عين قزام" => array("عين قزام", "تين زاواتين"),

        "55: ولاية تقرت" => array("تقرت", "تبسبست", "النزلة", "العالية", "المقارين", "سيدي سليمان", "الطيبات"),

        "56: ولاية جانت" => array("جانت", "برج الحواس"),

        "57: ولاية المغير" => array("المغير", "جامعة", "سيدي عمران", "تندلة", "سيدي خليل", "أم الطيور", "المرارة", "سطيل"),

        "58: ولاية المنيعة" => array("المنيعة", "حاسي القارة", "حاسي لفحل")
    );

    return $algeria_data;
}

/**
 * دالة للتوافق مع الكود القديم - استرجاع بيانات جميع الولايات
 *
 * @return array مصفوفة بالولايات والبلديات
 */
function form_elrakami_get_algeria_cities() {
    return form_elrakami_get_algeria_data();
}

/**
 * الحصول على قائمة الولايات فقط (بدون البلديات) - مرتبة من 1 إلى 58
 * مع إظهار رقم الولاية في النص المعروض
 *
 * @return array مصفوفة بأكواد وأسماء الولايات مع الأرقام مرتبة رقمياً
 */
function get_algeria_states() {
    static $states = null;

    if ($states !== null) {
        return $states;
    }

    $algeria_data = form_elrakami_get_algeria_data();
    $temp_states = array();

    foreach ($algeria_data as $state_key => $municipalities) {
        // استخراج كود الولاية واسمها
        $parts = explode(':', $state_key, 2);
        if (count($parts) >= 2) {
            $code = trim($parts[0]);
            $name = trim(str_replace('ولاية', '', $parts[1]));
            $temp_states[intval($code)] = array('code' => $code, 'name' => $name);
        }
    }

    // ترتيب حسب الرقم من 1 إلى 58
    ksort($temp_states, SORT_NUMERIC);

    // إنشاء مصفوفة مرتبة بالترتيب الصحيح للمفاتيح لضمان الترتيب في JSON
    $states = array();
    foreach ($temp_states as $state_data) {
        $display_name = $state_data['code'] . ' - ' . $state_data['name'];
        $states[$state_data['code']] = $display_name;
    }

    return $states;
}

/**
 * الحصول على اسم الولاية من رقمها
 *
 * @param string $state_code رقم الولاية (01, 02, ..., 58)
 * @return string اسم الولاية
 */
function get_state_name($state_code) {
    if (empty($state_code)) {
        return '';
    }

    // تنظيف وتحضير كود الولاية
    $state_code = sanitize_text_field($state_code);

    // إضافة صفر في البداية إذا كان رقماً من منزلة واحدة
    if (is_numeric($state_code) && strlen($state_code) === 1) {
        $state_code = '0' . $state_code;
    }

    $algeria_data = form_elrakami_get_algeria_data();

    foreach ($algeria_data as $state_key => $municipalities) {
        // استخراج كود الولاية من المفتاح (مثل "01: ولاية أدرار" -> "01")
        $code = substr($state_key, 0, 2);

        if ($code === $state_code) {
            // استخراج اسم الولاية من المفتاح
            $parts = explode(':', $state_key, 2);
            if (count($parts) >= 2) {
                return trim(str_replace('ولاية', '', $parts[1]));
            }
        }
    }

    return $state_code; // إرجاع الرقم إذا لم نجد الاسم
}

/**
 * استرجاع بيانات البلديات لولاية معينة - نسخة محسنة
 *
 * @param string $state كود الولاية (01, 02, ..., 58)
 * @return array مصفوفة بأسماء البلديات
 */
function get_municipalities_for_state($state) {
    // تنظيف وتحضير كود الولاية
    $state = sanitize_text_field($state);

    // إضافة صفر في البداية إذا كان رقماً من منزلة واحدة
    if (is_numeric($state) && strlen($state) === 1) {
        $state = '0' . $state;
    }

    // الحصول على البيانات من المصدر الوحيد
    $algeria_data = form_elrakami_get_algeria_data();

    // البحث عن الولاية المطلوبة
    foreach ($algeria_data as $state_key => $municipalities) {
        // استخراج كود الولاية من المفتاح (مثل "01: ولاية أدرار" -> "01")
        $state_code = substr($state_key, 0, 2);

        if ($state_code === $state) {
            return $municipalities;
        }
    }

    // إذا لم نجد الولاية، إرجاع مصفوفة فارغة
    return array();
}

/**
 * الحصول على اسم البلدية الكامل من رمز البلدية - نسخة محسنة
 *
 * @param string $municipality_code رمز البلدية
 * @param string $state رمز الولاية (اختياري)
 * @return string اسم البلدية الكامل
 */
function get_municipality_name($municipality_code, $state = '') {
    if (empty($municipality_code)) {
        return '';
    }

    // إذا تم تحديد ولاية، البحث فيها فقط
    if (!empty($state)) {
        $municipalities = get_municipalities_for_state($state);
        foreach ($municipalities as $municipality_name) {
            if (sanitize_title($municipality_name) === $municipality_code) {
                return $municipality_name;
            }
        }
    } else {
        // البحث في جميع الولايات
        $algeria_data = form_elrakami_get_algeria_data();
        foreach ($algeria_data as $municipalities) {
            foreach ($municipalities as $municipality_name) {
                if (sanitize_title($municipality_name) === $municipality_code) {
                    return $municipality_name;
                }
            }
        }
    }

    // إذا لم نجد مطابقة، إرجاع الرمز الأصلي
    return $municipality_code;
}
