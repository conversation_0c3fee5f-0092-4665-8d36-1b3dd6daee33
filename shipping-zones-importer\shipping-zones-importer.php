<?php
/*
Plugin Name: Shipping Zones Importer
Description: إضافة مناطق الشحن تلقائياً لجميع الولايات
Version: 1.0.1
Author: aissa houa
*/

defined('ABSPATH') || exit;

class Shipping_Zones_Importer {
    private $shipping_data = [];
    private $companies = [
        'zr_express' => [
            'name' => 'ZR Express',
            'file' => 'shopping-pricing.json',
            'enabled' => true
        ],
        'yalidine' => [
            'name' => 'Yalidine',
            'file' => 'yalidine-‏‏shopping-pricing.json',
            'enabled' => true
        ]
    ];

    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_ajax_import_shipping_zones', array($this, 'import_shipping_zones'));
        add_action('wp_ajax_upload_pricing_file', array($this, 'upload_pricing_file'));
        add_action('wp_ajax_add_shipping_company', array($this, 'add_shipping_company'));
        add_action('wp_ajax_toggle_shipping_company', array($this, 'toggle_shipping_company'));
        add_action('wp_ajax_delete_shipping_company', array($this, 'delete_shipping_company'));

        // تسجيل الولايات
        add_filter('woocommerce_states', array($this, 'add_dz_states'));

        // تحميل بيانات الشركات المخزنة
        $saved_companies = get_option('shipping_companies_data', []);
        if (!empty($saved_companies)) {
            foreach ($saved_companies as $company_id => $company) {
                if (!isset($company['enabled'])) {
                    $saved_companies[$company_id]['enabled'] = true;
                }
            }
            $this->companies = array_merge($this->companies, $saved_companies);
        }
    }

    public function add_admin_menu() {
        add_menu_page(
            'استيراد مناطق الشحن',
            'مناطق الشحن',
            'manage_options',
            'shipping-zones-importer',
            array($this, 'admin_page'),
            'dashicons-location-alt'
        );
    }

    public function enqueue_admin_assets($hook) {
        if($hook != 'toplevel_page_shipping-zones-importer') {
            return;
        }

        wp_enqueue_style('shipping-zones-importer', plugins_url('assets/css/admin.css', __FILE__));
        wp_enqueue_script('shipping-zones-importer', plugins_url('assets/js/admin.js', __FILE__), array('jquery'), '1.0', true);
        wp_localize_script('shipping-zones-importer', 'szimporter', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('shipping-zones-importer')
        ));
    }

    private function load_pricing_file($company_id) {
        $company = isset($this->companies[$company_id]) ? $this->companies[$company_id] : null;
        if (!$company) {
            return null;
        }

        $file_path = dirname(__FILE__) . '/' . $company['file'];
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            if ($content !== false) {
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // إضافة معرف الشركة واسمها للبيانات
                    foreach ($data as &$zone) {
                        $zone['company_id'] = $company_id;
                        $zone['company_name'] = $company['name'];
                    }
                    return $data;
                }
            }
        }
        return null;
    }

    private function load_all_pricing_files() {
        $all_data = [];
        $enabled_companies = 0;
        $loaded_companies = 0;

        foreach ($this->companies as $company_id => $company) {
            if ($company['enabled']) {
                $enabled_companies++;
                $company_data = $this->load_pricing_file($company_id);
                if ($company_data) {
                    $loaded_companies++;
                    foreach ($company_data as $item) {
                        // التأكد من وجود جميع الحقول المطلوبة
                        if (!isset($item['IDWilaya']) || !isset($item['Wilaya']) ||
                            !isset($item['Domicile']) || !isset($item['Stopdesk'])) {
                            continue;
                        }
                        $all_data[] = array_merge($item, [
                            'company_id' => $company_id,
                            'company_name' => $company['name']
                        ]);
                    }
                }
            }
        }

        if ($enabled_companies === 0) {
            throw new Exception('لا توجد شركات شحن مفعلة');
        }

        if ($loaded_companies === 0) {
            throw new Exception('لم يتم العثور على ملفات تسعير للشركات المفعلة');
        }

        if (empty($all_data)) {
            throw new Exception('لم يتم العثور على بيانات صالحة في ملفات التسعير');
        }

        return $all_data;
    }

    private function remove_company_shipping_methods($company_id) {
        if (!class_exists('WC_Shipping_Zones')) {
            return;
        }

        $company_name = $this->companies[$company_id]['name'];
        $shipping_zones = WC_Shipping_Zones::get_zones();

        foreach ($shipping_zones as $zone_data) {
            $zone = new WC_Shipping_Zone($zone_data['id']);
            $shipping_methods = $zone->get_shipping_methods();

            foreach ($shipping_methods as $method) {
                $title = $method->get_title();
                // حذف طرق الشحن التي تبدأ باسم الشركة
                if (strpos($title, $company_name) === 0) {
                    $zone->delete_shipping_method($method->get_instance_id());
                }
            }
        }
    }

    public function toggle_shipping_company() {
        check_ajax_referer('shipping-zones-importer');

        try {
            if (!isset($_POST['company_id'])) {
                throw new Exception('معرف الشركة غير موجود');
            }

            $company_id = sanitize_key($_POST['company_id']);
            if (!isset($this->companies[$company_id])) {
                throw new Exception('الشركة غير موجودة');
            }

            $saved_companies = get_option('shipping_companies_data', []);
            if (!isset($saved_companies[$company_id])) {
                $saved_companies[$company_id] = $this->companies[$company_id];
            }

            $new_status = !$saved_companies[$company_id]['enabled'];
            $saved_companies[$company_id]['enabled'] = $new_status;

            // إذا تم تعطيل الشركة، نقوم بحذف طرق الشحن الخاصة بها
            if (!$new_status) {
                $this->remove_company_shipping_methods($company_id);
            }

            update_option('shipping_companies_data', $saved_companies);

            wp_send_json_success([
                'message' => $new_status ? 'تم تفعيل الشركة' : 'تم تعطيل الشركة وحذف طرق الشحن الخاصة بها',
                'enabled' => $new_status
            ]);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    public function delete_shipping_company() {
        check_ajax_referer('shipping-zones-importer');

        try {
            if (!isset($_POST['company_id'])) {
                throw new Exception('معرف الشركة غير موجود');
            }

            $company_id = sanitize_key($_POST['company_id']);
            if (!isset($this->companies[$company_id])) {
                throw new Exception('الشركة غير موجودة');
            }

            $company_name = $this->companies[$company_id]['name'];

            // حذف طرق الشحن الخاصة بالشركة أولاً
            $this->remove_company_shipping_methods($company_id);

            // حذف ملف التسعير إذا كان موجوداً
            $file_path = dirname(__FILE__) . '/' . $this->companies[$company_id]['file'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }

            // حذف الشركة من قائمة الشركات المحفوظة
            $saved_companies = get_option('shipping_companies_data', []);
            unset($saved_companies[$company_id]);
            update_option('shipping_companies_data', $saved_companies);

            // حذف الشركة من المصفوفة المحلية
            unset($this->companies[$company_id]);

            wp_send_json_success(sprintf(
                'تم حذف شركة %s وإزالة جميع طرق الشحن الخاصة بها',
                $company_name
            ));

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    public function add_shipping_company() {
        check_ajax_referer('shipping-zones-importer');

        try {
            if (!isset($_POST['company_id']) || !isset($_POST['company_name'])) {
                throw new Exception('بيانات الشركة غير مكتملة');
            }

            $company_id = sanitize_key($_POST['company_id']);
            $company_name = sanitize_text_field($_POST['company_name']);

            if (isset($this->companies[$company_id])) {
                throw new Exception('معرف الشركة مستخدم بالفعل');
            }

            $this->companies[$company_id] = [
                'name' => $company_name,
                'file' => $company_id . '_pricing.json'
            ];

            update_option('shipping_companies_data', array_diff_key($this->companies, ['zr_express' => []]));
            wp_send_json_success('تمت إضافة الشركة بنجاح');

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>استيراد مناطق الشحن</h1>

            <div class="notice notice-warning">
                <p>يرجى التأكد من عمل نسخة احتياطية قبل الاستيراد</p>
            </div>

            <div class="card">
                <h2>شركات الشحن</h2>
                <div class="shipping-companies">
                    <?php foreach ($this->companies as $company_id => $company): ?>
                        <div class="company-item <?php echo !$company['enabled'] ? 'disabled' : ''; ?>" data-company="<?php echo esc_attr($company_id); ?>">
                            <div class="company-header">
                                <h3><?php echo esc_html($company['name']); ?></h3>
                                <div class="company-actions">
                                    <button type="button"
                                            class="button toggle-company"
                                            data-company="<?php echo esc_attr($company_id); ?>">
                                        <?php echo $company['enabled'] ? 'تعطيل' : 'تفعيل'; ?>
                                    </button>
                                    <button type="button"
                                            class="button button-danger delete-company"
                                            data-company="<?php echo esc_attr($company_id); ?>">
                                        حذف
                                    </button>
                                </div>
                            </div>

                            <div class="company-status">
                                <span class="status-indicator"></span>
                                <span class="status-text">
                                    <?php echo $company['enabled'] ? 'مفعلة' : 'معطلة'; ?>
                                </span>
                            </div>

                            <?php
                            $company_data = $this->load_pricing_file($company_id);
                            if ($company_data): ?>
                                <div class="notice notice-success inline">
                                    <p>تم العثور على ملف الأسعار (<?php echo esc_html($company['file']); ?>)</p>
                                </div>
                            <?php else: ?>
                                <div class="notice notice-warning inline">
                                    <p>لم يتم العثور على ملف الأسعار</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>

                <h3>إضافة شركة جديدة</h3>
                <form id="add-company-form" method="post">
                    <input type="text" name="company_id" placeholder="معرف الشركة (بالإنجليزية)" required>
                    <input type="text" name="company_name" placeholder="اسم الشركة" required>
                    <button type="submit" class="button">إضافة شركة</button>
                </form>

                <h3>رفع ملف الأسعار</h3>
                <form id="pricing-upload-form" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <select name="company_id" required>
                            <?php foreach ($this->companies as $id => $company): ?>
                                <option value="<?php echo esc_attr($id); ?>"><?php echo esc_html($company['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="file" name="pricing_file" id="pricing_file" accept=".json" required>
                        <button type="submit" class="button button-primary">رفع ملف الأسعار</button>
                    </div>
                    <p class="description">اختر الشركة وارفع ملف JSON الخاص بها</p>
                    <div id="file-preview"></div>
                </form>
            </div>

            <div class="card" style="margin-top: 20px;">
                <button id="import-shipping-zones" class="button button-primary">بدء الاستيراد</button>
                <div id="import-progress"></div>
            </div>
        </div>
        <?php
    }

    public function import_shipping_zones() {
        check_ajax_referer('shipping-zones-importer');

        try {
            if (!class_exists('WC_Shipping_Zone')) {
                throw new Exception('WooCommerce غير مثبت');
            }

            // تفعيل الجزائر كدولة للشحن
            update_option('woocommerce_allowed_countries', 'specific');
            update_option('woocommerce_specific_allowed_countries', array('DZ'));

            // مسح جميع مناطق الشحن القديمة
            $this->delete_existing_zones();

            // تجميع بيانات الشحن من جميع الشركات
            $this->shipping_data = $this->load_all_pricing_files();

            if (empty($this->shipping_data)) {
                throw new Exception('لم يتم العثور على بيانات الشحن');
            }

            // تنظيم البيانات حسب الولاية
            $companies_by_wilaya = [];
            foreach ($this->shipping_data as $data) {
                $wilaya_id = $data['IDWilaya'];
                if (!isset($companies_by_wilaya[$wilaya_id])) {
                    $companies_by_wilaya[$wilaya_id] = [];
                }
                $companies_by_wilaya[$wilaya_id][] = $data;
            }

            // إنشاء منطقة شحن لكل ولاية (58 ولاية)
            $states = $this->add_dz_states([])['DZ'];
            foreach ($states as $state_code => $state_name) {
                $wilaya_id = (int)substr($state_code, 3); // استخراج رقم الولاية من الكود DZ-XX

                // إنشاء منطقة الشحن
                $zone = new WC_Shipping_Zone();
                $zone->set_zone_name(preg_replace('/^\d+\s+-\s+/', '', $state_name)); // إزالة الرقم من اسم الولاية

                $location = array(
                    'type' => 'state',
                    'code' => 'DZ:' . $state_code
                );

                $zone->add_location($location['code'], $location['type']);
                $zone->save();

                // إضافة طرق الشحن المتوفرة من الشركات لهذه الولاية
                if (isset($companies_by_wilaya[$wilaya_id])) {
                    foreach ($companies_by_wilaya[$wilaya_id] as $company_data) {
                        if ($company_data['Domicile'] != "0") {
                            $this->add_shipping_method(
                                $zone,
                                $company_data['company_name'] . ' (توصيل إلى المنزل)',
                                $company_data['Domicile']
                            );
                        }

                        if ($company_data['Stopdesk'] != "0") {
                            $this->add_shipping_method(
                                $zone,
                                $company_data['company_name'] . ' (توصيل إلى المكتب)',
                                $company_data['Stopdesk']
                            );
                        }
                    }
                }
            }

            // تجميع إحصائيات عن العملية
            $stats = [
                'total_zones' => count($states),
                'companies' => [],
                'total_methods' => 0
            ];

            // حساب عدد طرق التوصيل لكل شركة
            foreach ($companies_by_wilaya as $wilaya_methods) {
                foreach ($wilaya_methods as $method) {
                    if (!isset($stats['companies'][$method['company_name']])) {
                        $stats['companies'][$method['company_name']] = [
                            'home_delivery' => 0,
                            'office_delivery' => 0
                        ];
                    }

                    if ($method['Domicile'] != "0") {
                        $stats['companies'][$method['company_name']]['home_delivery']++;
                        $stats['total_methods']++;
                    }
                    if ($method['Stopdesk'] != "0") {
                        $stats['companies'][$method['company_name']]['office_delivery']++;
                        $stats['total_methods']++;
                    }
                }
            }

            // إنشاء تقرير مفصل
            $report = "تم إنشاء {$stats['total_zones']} منطقة شحن\n\n";
            $report .= "إجمالي طرق التوصيل: {$stats['total_methods']}\n\n";
            $report .= "تفاصيل الشركات:\n";
            $report .= "══════════════\n\n";

            foreach ($stats['companies'] as $company => $methods) {
                $report .= "◉ {$company}:\n";
                $report .= "  • توصيل للمنزل: {$methods['home_delivery']} ولاية\n";
                $report .= "  • توصيل للمكتب: {$methods['office_delivery']} ولاية\n\n";
            }

            wp_send_json_success($report);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    private function delete_existing_zones() {
        global $wpdb;
        $wpdb->query("TRUNCATE TABLE {$wpdb->prefix}woocommerce_shipping_zones");
        $wpdb->query("TRUNCATE TABLE {$wpdb->prefix}woocommerce_shipping_zone_locations");
        $wpdb->query("TRUNCATE TABLE {$wpdb->prefix}woocommerce_shipping_zone_methods");
    }

    private function add_shipping_method($zone, $title, $cost) {
        $instance_id = $zone->add_shipping_method('flat_rate');
        $shipping_method = WC_Shipping_Zones::get_shipping_method($instance_id);

        if ($shipping_method) {
            $settings = array(
                'title' => $title,
                'cost' => $cost,
                'tax_status' => 'none',
                'enabled' => 'yes'
            );

            update_option('woocommerce_flat_rate_' . $instance_id . '_settings', $settings, 'yes');
        }
    }



    public function upload_pricing_file() {
        check_ajax_referer('shipping-zones-importer');

        try {
            if (!isset($_FILES['pricing_file']) || !isset($_POST['company_id'])) {
                throw new Exception('البيانات غير مكتملة');
            }

            $company_id = sanitize_key($_POST['company_id']);
            if (!isset($this->companies[$company_id])) {
                throw new Exception('الشركة غير موجودة');
            }

            $file = $_FILES['pricing_file'];
            if ($file['error']) {
                throw new Exception('حدث خطأ أثناء رفع الملف: ' . $file['error']);
            }

            // التحقق من صحة الملف
            $content = file_get_contents($file['tmp_name']);
            if ($content === false) {
                throw new Exception('فشل في قراءة محتوى الملف');
            }

            $data = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('الملف غير صالح: ' . json_last_error_msg());
            }

            // حفظ الملف في مجلد الإضافة
            $target_file = dirname(__FILE__) . '/' . $this->companies[$company_id]['file'];
            if (!move_uploaded_file($file['tmp_name'], $target_file)) {
                throw new Exception('فشل في حفظ الملف');
            }

            wp_send_json_success('تم رفع ملف أسعار ' . $this->companies[$company_id]['name'] . ' بنجاح');

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    public function add_dz_states($states) {
        $states['DZ'] = array(
            'DZ-01' => '01 Adrar - أدرار',
            'DZ-02' => '02 Chlef - الشلف',
            'DZ-03' => '03 Laghouat - الأغواط',
            'DZ-04' => '04 Oum El Bouaghi - أم البواقي',
            'DZ-05' => '05 Batna - باتنة',
            'DZ-06' => '06 Béjaïa - بجاية',
            'DZ-07' => '07 Biskra - بسكرة',
            'DZ-08' => '08 Bechar - بشار',
            'DZ-09' => '09 Blida - البليدة',
            'DZ-10' => '10 Bouira - البويرة',
            'DZ-11' => '11 Tamanrasset - تمنراست',
            'DZ-12' => '12 Tébessa - تبسة',
            'DZ-13' => '13 Tlemcene - تلمسان',
            'DZ-14' => '14 Tiaret - تيارت',
            'DZ-15' => '15 Tizi Ouzou - تيزي وزو',
            'DZ-16' => '16 Alger - الجزائر',
            'DZ-17' => '17 Djelfa - الجلفة',
            'DZ-18' => '18 Jijel - جيجل',
            'DZ-19' => '19 Sétif - سطيف',
            'DZ-20' => '20 Saïda - سعيدة',
            'DZ-21' => '21 Skikda - سكيكدة',
            'DZ-22' => '22 Sidi Bel Abbès - سيدي بلعباس',
            'DZ-23' => '23 Annaba - عنابة',
            'DZ-24' => '24 Guelma - قالمة',
            'DZ-25' => '25 Constantine - قسنطينة',
            'DZ-26' => '26 Médéa - المدية',
            'DZ-27' => '27 Mostaganem - مستغانم',
            'DZ-28' => '28 MSila - مسيلة',
            'DZ-29' => '29 Mascara - معسكر',
            'DZ-30' => '30 Ouargla - ورقلة',
            'DZ-31' => '31 Oran - وهران',
            'DZ-32' => '32 El Bayadh - البيض',
            'DZ-33' => '33 Illizi - إليزي',
            'DZ-34' => '34 Bordj Bou Arreridj - برج بوعريريج',
            'DZ-35' => '35 Boumerdès - بومرداس',
            'DZ-36' => '36 El Tarf - الطارف',
            'DZ-37' => '37 Tindouf - تندوف',
            'DZ-38' => '38 Tissemsilt - تيسمسيلت',
            'DZ-39' => '39 Eloued - الوادي',
            'DZ-40' => '40 Khenchela - خنشلة',
            'DZ-41' => '41 Souk Ahras - سوق أهراس',
            'DZ-42' => '42 Tipaza - تيبازة',
            'DZ-43' => '43 Mila - ميلة',
            'DZ-44' => '44 Aïn Defla - عين الدفلى',
            'DZ-45' => '45 Naâma - النعامة',
            'DZ-46' => '46 Aïn Témouchent - عين تموشنت',
            'DZ-47' => '47 Ghardaïa - غرداية',
            'DZ-48' => '48 Relizane - غليزان',
            'DZ-49' => '49 Timimoun - تيميمون',
            'DZ-50' => '50 Bordj Baji Mokhtar - برج باجي مختار',
            'DZ-51' => '51 Ouled Djellal - أولاد جلال',
            'DZ-52' => '52 Béni Abbès - بني عباس',
            'DZ-53' => '53 Aïn Salah - عين صالح',
            'DZ-54' => '54 In Guezzam - عين قزام',
            'DZ-55' => '55 Touggourt - تقرت',
            'DZ-56' => '56 Djanet - جانت',
            'DZ-57' => '57 El MGhair - المغير',
            'DZ-58' => '58 El Menia - المنيعة'
        );

        return $states;
    }
}

new Shipping_Zones_Importer();