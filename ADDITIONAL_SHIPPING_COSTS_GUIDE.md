# دليل رسوم التوصيل الإضافية - Form Elrakami

## 🎯 نظرة عامة

تتيح لك هذه الميزة إضافة رسوم توصيل إضافية لأي منتج أو متغير بشكل فردي ومباشر من صفحة تحرير المنتج، دون الحاجة لإعداد فئات شحن معقدة.

## ✨ المميزات

- **سهولة الاستخدام**: إضافة رسوم مباشرة من صفحة تحرير المنتج
- **مرونة كاملة**: رسوم مختلفة لكل منتج أو متغير
- **شفافية**: عرض واضح للرسوم الإضافية للعميل
- **حساب تلقائي**: التكلفة النهائية = التكلفة الأساسية + الرسوم الإضافية

## 🚀 كيفية الاستخدام

### للمنتجات البسيطة:

1. **اذهب إلى المنتجات > تحرير منتج**
2. **انتقل إلى تبويب "الشحن"**
3. **ستجد قسم "رسوم التوصيل الإضافية - Form Elrakami"**
4. **أدخل المبلغ الإضافي بالدينار الجزائري**
5. **أضف وصفاً اختيارياً للرسوم**
6. **احفظ المنتج**

### للمنتجات المتغيرة:

1. **اذهب إلى المنتجات > تحرير منتج متغير**
2. **انتقل إلى تبويب "المتغيرات"**
3. **افتح أي متغير للتحرير**
4. **في قسم "التسعير" ستجد "رسوم التوصيل الإضافية"**
5. **أدخل الرسوم المطلوبة لكل متغير**
6. **احفظ التغييرات**

## 💡 أمثلة عملية

### مثال 1: منتج ثقيل
- **المنتج**: ثلاجة كبيرة
- **تكلفة التوصيل الأساسية**: 500 دج
- **الرسوم الإضافية**: 300 دج
- **التكلفة النهائية**: 800 دج
- **الوصف**: "رسوم إضافية للأجهزة الكبيرة"

### مثال 2: منتج هش
- **المنتج**: مرآة زجاجية
- **تكلفة التوصيل الأساسية**: 400 دج
- **الرسوم الإضافية**: 150 دج
- **التكلفة النهائية**: 550 دج
- **الوصف**: "رسوم تغليف خاص للمنتجات الهشة"

### مثال 3: متغيرات مختلفة
- **المنتج**: طاولة خشبية
- **المتغير الصغير**: +0 دج (بدون رسوم إضافية)
- **المتغير المتوسط**: +100 دج
- **المتغير الكبير**: +200 دج

## 🔍 عرض الرسوم في قائمة المنتجات

- **عمود جديد**: "رسوم التوصيل الإضافية" في قائمة المنتجات
- **عرض سريع**: للمنتجات التي لها رسوم إضافية
- **الوصف**: يظهر تحت المبلغ إذا تم إضافته

## 🎨 واجهة المستخدم

### في صفحة تحرير المنتج:
- **تصميم واضح**: قسم منفصل بعنوان واضح
- **أيقونة مميزة**: رمز العربة للتمييز
- **رسالة توضيحية**: شرح كيفية عمل الرسوم
- **حقول بسيطة**: مبلغ + وصف اختياري

### في النموذج للعميل:
- **شفافية كاملة**: عرض التكلفة النهائية
- **تفصيل واضح**: التكلفة الأساسية + الرسوم الإضافية
- **وصف الرسوم**: إذا تم إضافته من المدير

## ⚙️ الإعدادات التقنية

### حقول البيانات:
- `_form_elrakami_additional_shipping_cost`: المبلغ الإضافي
- `_form_elrakami_shipping_cost_description`: وصف الرسوم

### أنواع المنتجات المدعومة:
- ✅ المنتجات البسيطة
- ✅ المنتجات المتغيرة
- ✅ المنتجات المجمعة (للمنتجات الفردية)

### التكامل:
- ✅ نظام WooCommerce الأساسي
- ✅ نظام السلة المخصص
- ✅ جميع طرق الشحن في الإضافة

## 🛠️ نصائح للاستخدام

### 1. التخطيط المسبق
- حدد المنتجات التي تحتاج رسوم إضافية
- ضع معايير واضحة للرسوم (الوزن، الحجم، الهشاشة)
- استخدم أوصاف واضحة للعملاء

### 2. الشفافية مع العملاء
- اذكر سبب الرسوم الإضافية
- استخدم أوصاف مفهومة
- كن متسقاً في التسعير

### 3. إدارة المخزون
- راجع الرسوم بانتظام
- حدث الأسعار حسب تغيرات التكاليف
- استخدم عمود قائمة المنتجات للمراجعة السريعة

## 🔧 استكشاف الأخطاء

### المشكلة: لا تظهر الرسوم الإضافية
**الحل**:
1. تأكد من حفظ المنتج بعد إضافة الرسوم
2. تحقق من أن المبلغ أكبر من 0
3. امسح الكاش إذا كنت تستخدم إضافات تخزين مؤقت

### المشكلة: الرسوم لا تظهر في النموذج
**الحل**:
1. تأكد من تحديث الإضافة لآخر إصدار
2. تحقق من سجلات الأخطاء في WordPress
3. تأكد من أن المنتج نشط ومتاح

### المشكلة: رسوم خاطئة للمتغيرات
**الحل**:
1. تأكد من إضافة الرسوم للمتغير وليس المنتج الأساسي
2. احفظ كل متغير بعد تعديل رسومه
3. تحقق من اختيار المتغير الصحيح في النموذج

## 📊 مراقبة الأداء

### تتبع الرسوم:
- استخدم عمود قائمة المنتجات للمراجعة
- راقب سجلات الأخطاء للتأكد من عمل النظام
- اختبر النموذج بانتظام مع منتجات مختلفة

### التحليل:
- راقب تأثير الرسوم على معدل التحويل
- اجمع ملاحظات العملاء حول وضوح الرسوم
- قارن الأرباح قبل وبعد تطبيق الرسوم

## 🎯 أفضل الممارسات

1. **كن واضحاً**: اشرح سبب الرسوم الإضافية
2. **كن عادلاً**: لا تبالغ في الرسوم
3. **كن متسقاً**: استخدم معايير ثابتة
4. **كن شفافاً**: اعرض التكلفة النهائية بوضوح
5. **كن مرناً**: راجع وحدث الرسوم حسب الحاجة

---

## 🆘 الدعم الفني

إذا واجهت أي مشاكل:
1. راجع سجلات الأخطاء في WordPress
2. تأكد من تحديث الإضافة
3. تواصل مع فريق الدعم الفني

**ملاحظة**: هذه الميزة تعمل مع جميع إصدارات WooCommerce المدعومة وتتكامل بسلاسة مع باقي ميزات Form Elrakami.
