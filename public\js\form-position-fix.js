/**
 * JavaScript to ensure the form is displayed correctly in all themes
 */
jQuery(document).ready(function($) {
    // Fix for Twenty Twenty-Four theme
    if ($('body').hasClass('theme-twentytwentyfour') || $('.wp-site-blocks').length > 0) {
        // Make sure the form is visible
        $('.form-elrakami-container').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'width': '100%',
            'max-width': '100%'
        });

        // Make sure the form is visible with proper styling
        $('.form-elrakami-form').css({
            'background-color': 'var(--form-bg-color, #ffffff)'
        });
    }

    // Fix for Botiga theme
    if ($('body').hasClass('botiga-site')) {
        $('.form-elrakami-container').css({
            'margin-top': '20px',
            'margin-bottom': '20px'
        });
    }

    // Fix for Blocksy theme
    if ($('body').hasClass('ct-theme')) {
        // تطبيق هوامش للفورم
        $('.form-elrakami-container').css({
            'margin-top': '20px',
            'margin-bottom': '20px'
        });

        // نقل الفورم ليظهر تحت السعر دائمًا في قالب بلوكسي
        setTimeout(function() {
            // البحث عن عنصر السعر في قالب بلوكسي
            var $priceElement = $('.entry-summary .price');

            if ($priceElement.length > 0) {
                // نقل الفورم ليظهر بعد السعر
                $('.form-elrakami-container').insertAfter($priceElement);
                console.log('تم نقل الفورم ليظهر بعد السعر في قالب بلوكسي');
            }
        }, 100);
    }

    // General fix for all themes
    // Make sure the form is always visible and centered
    setTimeout(function() {
        // Center the form
        $('.form-elrakami-container').css({
            'margin-left': 'auto',
            'margin-right': 'auto'
        });

        // Reduce side margins for better appearance
        $('.form-elrakami-form').css({
            'padding-left': '5px',
            'padding-right': '5px'
        });

        // محاولة وضع الفورم بعد السعر في جميع القوالب
        var $priceElement = $('.entry-summary .price, .summary .price, .product-summary .price');

        if ($priceElement.length > 0) {
            // نقل الفورم ليظهر بعد السعر
            $('.form-elrakami-container').insertAfter($priceElement);
            console.log('تم نقل الفورم ليظهر بعد السعر');
        }
        // إذا لم يتم العثور على عنصر السعر، نضع الفورم بعد عنوان المنتج
        else if ($('.form-elrakami-container:visible').length === 0) {
            $('.form-elrakami-container').insertAfter('.product_title');
        }
    }, 500);
});
