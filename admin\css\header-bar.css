/**
 * تنسيق شريط الشعار في أعلى صفحات الإضافة
 */

/* شريط الشعار الرئيسي */
.elrakami-header-bar {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    padding: 10px 15px;
    margin-bottom: 20px;
    border-left: 4px solid #3730a3;
}

/* تنسيق الشعار */
.elrakami-header-logo {
    flex: 0 0 auto;
    margin-left: 15px;
}

.elrakami-header-logo img {
    width: 40px;
    height: auto;
    display: block;
}

/* تنسيق المعلومات */
.elrakami-header-info {
    flex: 1;
}

.elrakami-header-info h2 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #3730a3;
}

.elrakami-header-info p {
    margin: 0;
    font-size: 13px;
    color: #64748b;
}

/* تنسيق الإصدار */
.elrakami-header-version {
    flex: 0 0 auto;
    background: #f8fafc;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #64748b;
    margin-right: 10px;
}

/* تنسيق الروابط */
.elrakami-header-links {
    display: flex;
    gap: 10px;
}

.elrakami-header-links a {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    color: #3730a3;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.elrakami-header-links a:hover {
    background: #e0e7ff;
}

.elrakami-header-links a .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-left: 4px;
}

/* تنسيق متوافق مع RTL */
.rtl .elrakami-header-logo {
    margin-left: 15px;
    margin-right: 0;
}

.rtl .elrakami-header-version {
    margin-right: 0;
    margin-left: 10px;
}

.rtl .elrakami-header-links a .dashicons {
    margin-left: 4px;
    margin-right: 0;
}
