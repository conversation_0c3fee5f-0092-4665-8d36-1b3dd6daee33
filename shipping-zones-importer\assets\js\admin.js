jQuery(document).ready(function($) {
    const $uploadForm = $('#pricing-upload-form');
    const $addCompanyForm = $('#add-company-form');
    const $fileInput = $('#pricing_file');
    const $importButton = $('#import-shipping-zones');
    const $progress = $('#import-progress');
    const $filePreview = $('#file-preview');

    // تفعيل/تعطيل شركة
    $(document).on('click', '.toggle-company', function() {
        const $button = $(this);
        const $companyItem = $button.closest('.company-item');
        const companyId = $button.data('company');

        $progress.removeClass('import-success import-error')
            .html('جاري تحديث حالة الشركة...')
            .show();

        $.ajax({
            url: szimporter.ajax_url,
            type: 'POST',
            data: {
                action: 'toggle_shipping_company',
                _ajax_nonce: szimporter.nonce,
                company_id: companyId
            },
            success: function(response) {
                if (response.success) {
                    $progress.addClass('import-success')
                        .html(response.data.message);
                    
                    // تحديث حالة الواجهة
                    if (response.data.enabled) {
                        $companyItem.removeClass('disabled');
                        $button.text('تعطيل');
                    } else {
                        $companyItem.addClass('disabled');
                        $button.text('تفعيل');
                    }
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ في الاتصال بالخادم');
            }
        });
    });

    // حذف شركة
    $(document).on('click', '.delete-company', function() {
        if (!confirm('هل أنت متأكد من حذف هذه الشركة؟')) {
            return;
        }

        const $button = $(this);
        const $companyItem = $button.closest('.company-item');
        const companyId = $button.data('company');

        $progress.removeClass('import-success import-error')
            .html('جاري حذف الشركة...')
            .show();

        $.ajax({
            url: szimporter.ajax_url,
            type: 'POST',
            data: {
                action: 'delete_shipping_company',
                _ajax_nonce: szimporter.nonce,
                company_id: companyId
            },
            success: function(response) {
                if (response.success) {
                    $progress.addClass('import-success')
                        .html(response.data);
                    $companyItem.slideUp(400, function() {
                        $(this).remove();
                    });
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ في الاتصال بالخادم');
            }
        });
    });

    // إضافة شركة جديدة
    $addCompanyForm.on('submit', function(e) {
        e.preventDefault();

        const companyId = $addCompanyForm.find('input[name="company_id"]').val();
        const companyName = $addCompanyForm.find('input[name="company_name"]').val();

        $progress.removeClass('import-success import-error')
            .html('جاري إضافة الشركة...')
            .show();

        $.ajax({
            url: szimporter.ajax_url,
            type: 'POST',
            data: {
                action: 'add_shipping_company',
                _ajax_nonce: szimporter.nonce,
                company_id: companyId,
                company_name: companyName
            },
            success: function(response) {
                if (response.success) {
                    $progress.addClass('import-success')
                        .html(response.data);
                    // تحديث الصفحة لإظهار الشركة الجديدة
                    location.reload();
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ في الاتصال بالخادم');
            }
        });
    });

    // معاينة ملف التسعير قبل الرفع
    $fileInput.on('change', function() {
        const file = this.files[0];
        if (!file) {
            $filePreview.empty();
            return;
        }

        // التحقق من نوع وحجم الملف
        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            alert('يرجى اختيار ملف JSON صالح');
            this.value = '';
            $filePreview.empty();
            return;
        }

        if (file.size > 5242880) { // 5 ميجابايت
            alert('حجم الملف كبير جداً. الحد الأقصى هو 5 ميجابايت');
            this.value = '';
            $filePreview.empty();
            return;
        }

        // قراءة وعرض معاينة للملف
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);
                if (!Array.isArray(data)) {
                    throw new Error('يجب أن يكون الملف مصفوفة من البيانات');
                }

                // عرض معاينة البيانات
                const previewHtml = `
                    <div class="file-preview-info">
                        <strong>معاينة الملف:</strong><br>
                        اسم الملف: ${file.name}<br>
                        عدد المناطق: ${data.length}<br>
                        حجم الملف: ${(file.size / 1024).toFixed(2)} كيلوبايت
                    </div>
                `;
                $filePreview.html(previewHtml);

            } catch (error) {
                alert('الملف غير صالح: ' + error.message);
                this.value = '';
                $filePreview.empty();
            }
        };
        reader.readAsText(file);
    });

    // معالجة رفع ملف التسعير
    $uploadForm.on('submit', function(e) {
        e.preventDefault();
        
        const file = $fileInput[0].files[0];
        if (!file) {
            alert('يرجى اختيار ملف');
            return;
        }

        // التحقق من الملف قبل الرفع
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);
                if (!Array.isArray(data)) {
                    throw new Error('يجب أن يكون الملف مصفوفة من البيانات');
                }

                // التحقق من صحة بنية البيانات
                const isValidStructure = data.every(item =>
                    item.hasOwnProperty('IDWilaya') &&
                    item.hasOwnProperty('Wilaya') &&
                    item.hasOwnProperty('Domicile') &&
                    item.hasOwnProperty('Stopdesk')
                );

                if (!isValidStructure) {
                    throw new Error('بنية البيانات غير صحيحة. يجب أن يحتوي كل عنصر على الحقول: IDWilaya, Wilaya, Domicile, Stopdesk');
                }

                // إرسال الملف إلى السيرفر
                const formData = new FormData($uploadForm[0]);
                formData.append('action', 'upload_pricing_file');
                formData.append('_ajax_nonce', szimporter.nonce);

                $progress.removeClass('import-success import-error')
                    .html('جاري رفع الملف...')
                    .show();

                // تعطيل زر الرفع أثناء العملية
                const $submitButton = $uploadForm.find('button[type="submit"]');
                $submitButton.prop('disabled', true);

                $.ajax({
                    url: szimporter.ajax_url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $progress.addClass('import-success')
                                .html(response.data);
                            // تحديث الصفحة لتحديث حالة الملفات
                            location.reload();
                        } else {
                            $progress.addClass('import-error')
                                .html('خطأ: ' + response.data);
                        }
                    },
                    error: function() {
                        $progress.addClass('import-error')
                            .html('حدث خطأ أثناء رفع الملف');
                    }
                });

            } catch (error) {
                alert('الملف غير صالح: ' + error.message);
                $fileInput.val('');
            }
        };
        reader.readAsText(file);
    });

    // استيراد مناطق الشحن
    function formatMessage(message) {
        return message.replace(/\n/g, '<br>').replace(/══════════════/, '<hr>')
            .replace(/◉\s([^:]+):/g, '<strong>◉ $1:</strong>')
            .replace(/•\s([^:]+):/g, '<span style="color:#666">• $1:</span>');
    }

    function startImport() {
        if (!confirm('سيتم حذف جميع مناطق الشحن الحالية وإعادة إنشائها. هل تريد المتابعة؟')) {
            return;
        }

        $importButton.prop('disabled', true);
        $progress.removeClass('import-success import-error')
            .html('<div class="importing">جاري استيراد مناطق الشحن...<br>يرجى الانتظار</div>')
            .show();
        
        $.ajax({
            url: szimporter.ajax_url,
            type: 'POST',
            data: {
                action: 'import_shipping_zones',
                _ajax_nonce: szimporter.nonce
            },
            success: function(response) {
                if(response.success) {
                    $progress.addClass('import-success')
                        .html(formatMessage(response.data));
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ في الاتصال بالخادم');
            },
            complete: function() {
                $importButton.prop('disabled', false);
            }
        });
    }

    $importButton.on('click', startImport);
});