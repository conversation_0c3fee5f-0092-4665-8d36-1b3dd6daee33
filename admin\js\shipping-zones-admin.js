/**
 * JavaScript لإدارة طرق التوصيل
 */

jQuery(document).ready(function($) {
    const $uploadForm = $('#pricing-upload-form');
    const $addCompanyForm = $('#add-company-form');
    const $fileInput = $('#pricing_file');
    const $importButton = $('#import-shipping-zones');
    const $progress = $('#import-progress');
    const $filePreview = $('#file-preview');
    const $editorModal = $('#pricing-file-editor-modal');
    const $editorContent = $('#pricing-file-content');
    let currentCompanyId = null;

    // تفعيل/تعطيل شركة
    $(document).on('click', '.toggle-company', function() {
        const $button = $(this);
        const $companyItem = $button.closest('.company-item');
        const companyId = $button.data('company');

        $progress.removeClass('import-success import-error')
            .html('جاري تحديث حالة الشركة...')
            .show();

        $.ajax({
            url: formElrakamAdmin.ajax_url,
            type: 'POST',
            data: {
                action: 'form_elrakami_toggle_shipping_company',
                _ajax_nonce: formElrakamAdmin.nonce,
                company_id: companyId
            },
            success: function(response) {
                if (response.success) {
                    $progress.addClass('import-success')
                        .html(response.data.message);

                    // تحديث حالة الواجهة
                    if (response.data.enabled) {
                        $companyItem.removeClass('disabled');
                        $button.text('تعطيل');
                        $companyItem.find('.status-text').text('مفعلة');
                    } else {
                        $companyItem.addClass('disabled');
                        $button.text('تفعيل');
                        $companyItem.find('.status-text').text('معطلة');
                    }
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ في الاتصال بالخادم');
            }
        });
    });

    // حذف شركة
    $(document).on('click', '.delete-company', function() {
        if (!confirm('هل أنت متأكد من حذف هذه الشركة؟\nسيتم حذف جميع طرق الشحن الخاصة بها.')) {
            return;
        }

        const $button = $(this);
        const $companyItem = $button.closest('.company-item');
        const companyId = $button.data('company');

        $progress.removeClass('import-success import-error')
            .html('جاري حذف الشركة...')
            .show();

        $.ajax({
            url: formElrakamAdmin.ajax_url,
            type: 'POST',
            data: {
                action: 'form_elrakami_delete_shipping_company',
                _ajax_nonce: formElrakamAdmin.nonce,
                company_id: companyId
            },
            success: function(response) {
                if (response.success) {
                    $progress.addClass('import-success')
                        .html(response.data);
                    $companyItem.slideUp(400, function() {
                        $(this).remove();
                    });
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ في الاتصال بالخادم');
            }
        });
    });

    // إضافة شركة جديدة
    $addCompanyForm.on('submit', function(e) {
        e.preventDefault();

        const companyId = $addCompanyForm.find('input[name="company_id"]').val();
        const companyName = $addCompanyForm.find('input[name="company_name"]').val();

        if (!companyId || !companyName) {
            alert('يرجى ملء جميع الحقول');
            return;
        }

        $progress.removeClass('import-success import-error')
            .html('جاري إضافة الشركة...')
            .show();

        $.ajax({
            url: formElrakamAdmin.ajax_url,
            type: 'POST',
            data: {
                action: 'form_elrakami_add_shipping_company',
                _ajax_nonce: formElrakamAdmin.nonce,
                company_id: companyId,
                company_name: companyName
            },
            success: function(response) {
                if (response.success) {
                    $progress.addClass('import-success')
                        .html(response.data);
                    // تحديث الصفحة لإظهار الشركة الجديدة
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ في الاتصال بالخادم');
            }
        });
    });

    // معاينة ملف التسعير قبل الرفع
    $fileInput.on('change', function() {
        const file = this.files[0];
        if (!file) {
            $filePreview.empty();
            return;
        }

        // التحقق من نوع وحجم الملف
        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            alert('يرجى اختيار ملف JSON صالح');
            this.value = '';
            $filePreview.empty();
            return;
        }

        if (file.size > 5242880) { // 5 ميجابايت
            alert('حجم الملف كبير جداً. الحد الأقصى هو 5 ميجابايت');
            this.value = '';
            $filePreview.empty();
            return;
        }

        // قراءة وعرض معاينة للملف
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);
                if (!Array.isArray(data)) {
                    throw new Error('يجب أن يكون الملف مصفوفة من البيانات');
                }

                // التحقق من صحة بنية البيانات
                const isValidStructure = data.every(item =>
                    item.hasOwnProperty('IDWilaya') &&
                    item.hasOwnProperty('Wilaya') &&
                    item.hasOwnProperty('Domicile') &&
                    item.hasOwnProperty('Stopdesk')
                );

                if (!isValidStructure) {
                    throw new Error('بنية البيانات غير صحيحة. يجب أن يحتوي كل عنصر على الحقول: IDWilaya, Wilaya, Domicile, Stopdesk');
                }

                // عرض معاينة البيانات
                const previewHtml = `
                    <div class="file-preview-info">
                        <strong>معاينة الملف:</strong>
                        اسم الملف: ${file.name}<br>
                        عدد المناطق: ${data.length}<br>
                        حجم الملف: ${(file.size / 1024).toFixed(2)} كيلوبايت<br>
                        <small>البيانات صالحة ✓</small>
                    </div>
                `;
                $filePreview.html(previewHtml);

            } catch (error) {
                alert('الملف غير صالح: ' + error.message);
                $fileInput.val('');
                $filePreview.empty();
            }
        };
        reader.readAsText(file);
    });

    // معالجة رفع ملف التسعير
    $uploadForm.on('submit', function(e) {
        e.preventDefault();

        const file = $fileInput[0].files[0];
        if (!file) {
            alert('يرجى اختيار ملف');
            return;
        }

        // إرسال الملف إلى السيرفر
        const formData = new FormData($uploadForm[0]);
        formData.append('action', 'form_elrakami_upload_pricing_file');
        formData.append('_ajax_nonce', formElrakamAdmin.nonce);

        $progress.removeClass('import-success import-error')
            .html('جاري رفع الملف...')
            .show();

        // تعطيل زر الرفع أثناء العملية
        const $submitButton = $uploadForm.find('button[type="submit"]');
        $submitButton.prop('disabled', true);

        $.ajax({
            url: formElrakamAdmin.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $progress.addClass('import-success')
                        .html(response.data);
                    // تحديث الصفحة لتحديث حالة الملفات
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ أثناء رفع الملف');
            },
            complete: function() {
                $submitButton.prop('disabled', false);
            }
        });
    });

    // استيراد مناطق الشحن
    function formatMessage(message) {
        return message.replace(/\n/g, '<br>').replace(/══════════════/, '<hr>')
            .replace(/◉\s([^:]+):/g, '<strong>◉ $1:</strong>')
            .replace(/•\s([^:]+):/g, '<span style="color:#666">• $1:</span>');
    }

    function startImport() {
        if (!confirm('سيتم حذف جميع مناطق الشحن الحالية وإعادة إنشائها.\n\nهل تريد المتابعة؟')) {
            return;
        }

        $importButton.prop('disabled', true);
        $progress.removeClass('import-success import-error')
            .html('<div class="importing">جاري استيراد مناطق الشحن...<br>يرجى الانتظار، قد تستغرق العملية عدة دقائق</div>')
            .show();

        $.ajax({
            url: formElrakamAdmin.ajax_url,
            type: 'POST',
            data: {
                action: 'form_elrakami_import_shipping_zones',
                _ajax_nonce: formElrakamAdmin.nonce
            },
            timeout: 300000, // 5 دقائق
            success: function(response) {
                if(response.success) {
                    $progress.addClass('import-success')
                        .html('<strong>تم الاستيراد بنجاح!</strong><br><br>' + formatMessage(response.data));
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'حدث خطأ في الاتصال بالخادم';
                if (status === 'timeout') {
                    errorMessage = 'انتهت مهلة الاتصال. قد تكون العملية قد اكتملت، يرجى التحقق من مناطق الشحن في WooCommerce.';
                }
                $progress.addClass('import-error')
                    .html(errorMessage);
            },
            complete: function() {
                $importButton.prop('disabled', false);
            }
        });
    }

    $importButton.on('click', startImport);

    // تحسين تجربة المستخدم
    $(document).on('click', '.import-success, .import-error', function() {
        $(this).slideUp();
    });

    // إضافة تلميحات للمساعدة
    $('[data-tooltip]').each(function() {
        $(this).attr('title', $(this).data('tooltip'));
    });

    // فتح محرر ملف الأسعار
    $(document).on('click', '.edit-pricing-file', function() {
        const companyId = $(this).data('company');
        currentCompanyId = companyId;
        openPricingFileEditor(companyId);
    });

    // إغلاق المحرر
    $(document).on('click', '.pricing-editor-close, .pricing-editor-overlay', function() {
        closePricingFileEditor();
    });

    // منع إغلاق المحرر عند النقر على المحتوى
    $(document).on('click', '.pricing-editor-content', function(e) {
        e.stopPropagation();
    });

    // التحقق من صحة JSON
    $('#validate-json').on('click', function() {
        validateJSON();
    });

    // تنسيق JSON
    $('#format-json').on('click', function() {
        formatJSON();
    });

    // حفظ ملف الأسعار
    $('#save-pricing-file').on('click', function() {
        savePricingFile();
    });

    // وظيفة فتح محرر ملف الأسعار
    function openPricingFileEditor(companyId) {
        $progress.removeClass('import-success import-error')
            .html('جاري تحميل ملف الأسعار...')
            .show();

        $.ajax({
            url: formElrakamAdmin.ajax_url,
            type: 'POST',
            data: {
                action: 'form_elrakami_get_pricing_file_content',
                _ajax_nonce: formElrakamAdmin.nonce,
                company_id: companyId
            },
            success: function(response) {
                $progress.hide();
                if (response.success) {
                    // تحديث معلومات المحرر
                    $('#editor-company-name').text(response.data.company_name);
                    $('#editor-record-count').text(response.data.record_count || 'جديد');
                    $('#editor-file-status').text(response.data.file_exists ? 'موجود' : 'جديد');

                    // تحميل المحتوى
                    $editorContent.val(response.data.content);

                    // إظهار المحرر
                    $editorModal.fadeIn(300);

                    // التركيز على المحرر
                    setTimeout(() => {
                        $editorContent.focus();
                    }, 350);
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ في الاتصال بالخادم');
            }
        });
    }

    // وظيفة إغلاق المحرر
    function closePricingFileEditor() {
        $editorModal.fadeOut(300);
        currentCompanyId = null;
        $editorContent.val('');
        $('#validation-result').removeClass('success error').hide();
    }

    // وظيفة التحقق من صحة JSON
    function validateJSON() {
        const content = $editorContent.val().trim();
        const $result = $('#validation-result');

        if (!content) {
            $result.removeClass('success').addClass('error')
                .text('المحتوى فارغ').show();
            return false;
        }

        try {
            const data = JSON.parse(content);

            if (!Array.isArray(data)) {
                throw new Error('يجب أن يكون الملف مصفوفة من البيانات');
            }

            // التحقق من بنية البيانات
            const requiredFields = ['IDWilaya', 'Wilaya', 'Domicile', 'Stopdesk'];
            for (let i = 0; i < data.length; i++) {
                const item = data[i];

                for (const field of requiredFields) {
                    if (!item.hasOwnProperty(field)) {
                        throw new Error(`العنصر رقم ${i + 1} يفتقد للحقل المطلوب: ${field}`);
                    }
                }

                if (!Number.isInteger(item.IDWilaya) || item.IDWilaya < 1 || item.IDWilaya > 58) {
                    throw new Error(`العنصر رقم ${i + 1} يحتوي على IDWilaya غير صالح: ${item.IDWilaya}`);
                }
            }

            $result.removeClass('error').addClass('success')
                .text(`JSON صالح ✓ (${data.length} سجل)`).show();
            return true;

        } catch (error) {
            $result.removeClass('success').addClass('error')
                .text('خطأ في JSON: ' + error.message).show();
            return false;
        }
    }

    // وظيفة تنسيق JSON
    function formatJSON() {
        const content = $editorContent.val().trim();

        if (!content) {
            alert('لا يوجد محتوى لتنسيقه');
            return;
        }

        try {
            const data = JSON.parse(content);
            const formatted = JSON.stringify(data, null, 2);
            $editorContent.val(formatted);

            $('#validation-result').removeClass('error').addClass('success')
                .text('تم تنسيق JSON بنجاح ✓').show();

        } catch (error) {
            $('#validation-result').removeClass('success').addClass('error')
                .text('لا يمكن تنسيق JSON غير صالح: ' + error.message).show();
        }
    }

    // وظيفة حفظ ملف الأسعار
    function savePricingFile() {
        if (!currentCompanyId) {
            alert('خطأ: لم يتم تحديد الشركة');
            return;
        }

        // التحقق من صحة JSON أولاً
        if (!validateJSON()) {
            alert('يرجى إصلاح أخطاء JSON قبل الحفظ');
            return;
        }

        const content = $editorContent.val().trim();

        $progress.removeClass('import-success import-error')
            .html('جاري حفظ الملف...')
            .show();

        // تعطيل زر الحفظ أثناء العملية
        const $saveButton = $('#save-pricing-file');
        $saveButton.prop('disabled', true);

        $.ajax({
            url: formElrakamAdmin.ajax_url,
            type: 'POST',
            data: {
                action: 'form_elrakami_save_pricing_file_content',
                _ajax_nonce: formElrakamAdmin.nonce,
                company_id: currentCompanyId,
                content: content
            },
            success: function(response) {
                if (response.success) {
                    $progress.addClass('import-success')
                        .html(response.data.message +
                              `<br>عدد السجلات: ${response.data.record_count}` +
                              `<br>حجم الملف: ${response.data.file_size}`);

                    // تحديث معلومات المحرر
                    $('#editor-record-count').text(response.data.record_count);
                    $('#editor-file-status').text('موجود');

                    // إغلاق المحرر بعد ثانيتين
                    setTimeout(() => {
                        closePricingFileEditor();
                        // تحديث الصفحة لإظهار التغييرات
                        location.reload();
                    }, 2000);
                } else {
                    $progress.addClass('import-error')
                        .html('خطأ: ' + response.data);
                }
            },
            error: function() {
                $progress.addClass('import-error')
                    .html('حدث خطأ أثناء حفظ الملف');
            },
            complete: function() {
                $saveButton.prop('disabled', false);
            }
        });
    }

    // حفظ إعدادات فئات الشحن
    $('#shipping-class-costs-form').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $result = $('#shipping-class-costs-result');
        const $submitButton = $form.find('button[type="submit"]');

        // جمع بيانات النموذج
        const formData = new FormData($form[0]);
        formData.append('action', 'form_elrakami_save_shipping_class_costs');
        formData.append('_ajax_nonce', formElrakamAdmin.nonce);

        // تعطيل زر الحفظ أثناء العملية
        $submitButton.prop('disabled', true).text('جاري الحفظ...');

        // إخفاء النتائج السابقة
        $result.removeClass('notice-success notice-error').hide();

        $.ajax({
            url: formElrakamAdmin.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $result.addClass('notice notice-success')
                        .html('<p>' + response.data + '</p>')
                        .show();
                } else {
                    $result.addClass('notice notice-error')
                        .html('<p>خطأ: ' + response.data + '</p>')
                        .show();
                }
            },
            error: function() {
                $result.addClass('notice notice-error')
                    .html('<p>حدث خطأ في الاتصال بالخادم</p>')
                    .show();
            },
            complete: function() {
                $submitButton.prop('disabled', false).text('حفظ إعدادات فئات الشحن');

                // إخفاء الرسالة بعد 3 ثوان
                setTimeout(function() {
                    $result.fadeOut();
                }, 3000);
            }
        });
    });

    // اختصارات لوحة المفاتيح للمحرر
    $(document).on('keydown', function(e) {
        if ($editorModal.is(':visible')) {
            // Ctrl+S للحفظ
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                savePricingFile();
            }
            // Escape للإغلاق
            else if (e.key === 'Escape') {
                closePricingFileEditor();
            }
            // Ctrl+Shift+F للتنسيق
            else if (e.ctrlKey && e.shiftKey && e.key === 'F') {
                e.preventDefault();
                formatJSON();
            }
        }
    });
});
