<?php
/**
 * إدارة ميتا بوكس إعدادات السلة في صفحة المنتج
 *
 * @since      1.0.0
 * @package    Form_Elrakami
 */

// منع الوصول المباشر
if (!defined('WPINC')) {
    die;
}

/**
 * صنف إدارة ميتا بوكس السلة
 */
class Cart_Metabox {

    /**
     * تهيئة الصنف
     */
    public function __construct() {
        // إضافة ميتا بوكس في صفحة المنتج
        add_action('add_meta_boxes', array($this, 'add_cart_metabox'));
        
        // حفظ بيانات المنتج
        add_action('save_post', array($this, 'save_cart_metabox_data'));
        
        // إضافة أنماط CSS وJavaScript للإدارة
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));

        // إضافة معالجات الإجراءات الجماعية
        $this->handle_bulk_cart_actions();

        // تفعيل زر السلة تلقائياً للمنتجات الجديدة
        add_action('wp_insert_post', array($this, 'auto_enable_cart_for_new_products'), 10, 3);
    }

    /**
     * إضافة ميتا بوكس السلة
     */
    public function add_cart_metabox() {
        add_meta_box(
            'form_elrakami_cart_settings',
            'إعدادات السلة المخصصة',
            array($this, 'cart_metabox_callback'),
            'product',
            'side',
            'default'
        );
    }

    /**
     * محتوى ميتا بوكس السلة
     */
    public function cart_metabox_callback($post) {
        // إضافة nonce للأمان
        wp_nonce_field('form_elrakami_cart_metabox', 'form_elrakami_cart_metabox_nonce');
        
        // الحصول على القيم المحفوظة - مع تفعيل زر السلة افتراضياً
        $show_add_to_cart = get_post_meta($post->ID, '_form_elrakami_show_add_to_cart', true);
        // إذا لم يتم تعيين قيمة من قبل، اجعل الافتراضي "yes"
        if (empty($show_add_to_cart)) {
            $show_add_to_cart = 'yes';
        }
        $cart_button_text = get_post_meta($post->ID, '_form_elrakami_cart_button_text', true);
        $cart_button_color = get_post_meta($post->ID, '_form_elrakami_cart_button_color', true);

        // القيم الافتراضية من الإعدادات العامة
        if (empty($cart_button_text)) {
            $cart_button_text = get_option('form_elrakami_cart_button_default_text', 'أضف إلى السلة');
        }
        if (empty($cart_button_color)) {
            $cart_button_color = get_option('form_elrakami_cart_button_default_color', '#28a745');
        }
        ?>
        
        <div class="form-elrakami-cart-metabox">
            <table class="form-table">
                <tr>
                    <td>
                        <label for="form_elrakami_show_add_to_cart">
                            <input type="checkbox" 
                                   id="form_elrakami_show_add_to_cart" 
                                   name="form_elrakami_show_add_to_cart" 
                                   value="yes" 
                                   <?php checked($show_add_to_cart, 'yes'); ?> />
                            <strong>إظهار زر "أضف إلى السلة"</strong>
                        </label>
                        <p class="description">عند تفعيل هذا الخيار، سيظهر زر "أضف إلى السلة" في نموذج الطلب تحت زر الطلب الرئيسي.</p>
                    </td>
                </tr>
            </table>
            
            <div id="cart_additional_settings" style="<?php echo ($show_add_to_cart !== 'yes') ? 'display: none;' : ''; ?>">
                <hr>
                <h4>إعدادات إضافية للسلة</h4>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="form_elrakami_cart_button_text">نص الزر</label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="form_elrakami_cart_button_text" 
                                   name="form_elrakami_cart_button_text" 
                                   value="<?php echo esc_attr($cart_button_text); ?>" 
                                   class="regular-text" />
                            <p class="description">النص الذي سيظهر على زر إضافة إلى السلة.</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="form_elrakami_cart_button_color">لون الزر</label>
                        </th>
                        <td>
                            <input type="color"
                                   id="form_elrakami_cart_button_color"
                                   name="form_elrakami_cart_button_color"
                                   value="<?php echo esc_attr($cart_button_color); ?>" />
                            <p class="description">لون خلفية زر إضافة إلى السلة.</p>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <style>
        .form-elrakami-cart-metabox .form-table th {
            width: 120px;
            padding: 10px 0;
        }
        .form-elrakami-cart-metabox .form-table td {
            padding: 10px 0;
        }
        .form-elrakami-cart-metabox h4 {
            margin: 15px 0 10px 0;
            color: #23282d;
        }
        .form-elrakami-cart-metabox hr {
            margin: 15px 0;
            border: none;
            border-top: 1px solid #ddd;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            $('#form_elrakami_show_add_to_cart').change(function() {
                if ($(this).is(':checked')) {
                    $('#cart_additional_settings').slideDown();
                } else {
                    $('#cart_additional_settings').slideUp();
                }
            });
        });
        </script>
        <?php
    }

    /**
     * حفظ بيانات ميتا بوكس السلة
     */
    public function save_cart_metabox_data($post_id) {
        // التحقق من الأمان
        if (!isset($_POST['form_elrakami_cart_metabox_nonce']) || 
            !wp_verify_nonce($_POST['form_elrakami_cart_metabox_nonce'], 'form_elrakami_cart_metabox')) {
            return;
        }

        // التحقق من صلاحيات المستخدم
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // التحقق من أن هذا منتج
        if (get_post_type($post_id) !== 'product') {
            return;
        }

        // حفظ إعداد إظهار زر السلة
        $show_add_to_cart = isset($_POST['form_elrakami_show_add_to_cart']) ? 'yes' : 'no';
        update_post_meta($post_id, '_form_elrakami_show_add_to_cart', $show_add_to_cart);

        // حفظ نص الزر
        if (isset($_POST['form_elrakami_cart_button_text'])) {
            $button_text = sanitize_text_field($_POST['form_elrakami_cart_button_text']);
            update_post_meta($post_id, '_form_elrakami_cart_button_text', $button_text);
        }

        // حفظ لون الزر
        if (isset($_POST['form_elrakami_cart_button_color'])) {
            $button_color = sanitize_hex_color($_POST['form_elrakami_cart_button_color']);
            update_post_meta($post_id, '_form_elrakami_cart_button_color', $button_color);
        }
    }

    /**
     * تحميل أصول الإدارة
     */
    public function enqueue_admin_assets($hook) {
        // تحميل الأصول فقط في صفحة تحرير المنتج أو صفحة المنتجات
        if ($hook !== 'post.php' && $hook !== 'post-new.php' && $hook !== 'edit.php') {
            return;
        }

        global $post;
        if ($hook === 'edit.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
            // في صفحة قائمة المنتجات
            wp_enqueue_script('jquery');
            $this->add_bulk_actions_script();
        } elseif ($post && get_post_type($post->ID) === 'product') {
            // في صفحة تحرير المنتج
            wp_enqueue_script('jquery');
        }
    }

    /**
     * إضافة سكريبت للإجراءات الجماعية
     */
    private function add_bulk_actions_script() {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // إضافة أزرار الإجراءات الجماعية
            if ($('.tablenav.top .alignleft.actions').length) {
                var bulkCartActions = '<div class="alignleft actions" style="margin-left: 10px;">' +
                    '<button type="button" id="bulk-enable-cart" class="button">تفعيل السلة للكل</button> ' +
                    '<button type="button" id="bulk-disable-cart" class="button">إلغاء السلة للكل</button>' +
                    '</div>';
                $('.tablenav.top .alignleft.actions').first().after(bulkCartActions);
            }

            // معالجة تفعيل السلة للكل
            $(document).on('click', '#bulk-enable-cart', function() {
                if (confirm('هل أنت متأكد من تفعيل زر السلة لجميع المنتجات؟')) {
                    bulkUpdateCartStatus('enable');
                }
            });

            // معالجة إلغاء السلة للكل
            $(document).on('click', '#bulk-disable-cart', function() {
                if (confirm('هل أنت متأكد من إلغاء تفعيل زر السلة لجميع المنتجات؟')) {
                    bulkUpdateCartStatus('disable');
                }
            });

            function bulkUpdateCartStatus(action) {
                var $button = $('#bulk-' + action + '-cart');
                var originalText = $button.text();
                $button.text('جاري المعالجة...').prop('disabled', true);

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'bulk_update_cart_status',
                        cart_action: action,
                        nonce: '<?php echo wp_create_nonce('bulk_cart_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('تم ' + (action === 'enable' ? 'تفعيل' : 'إلغاء تفعيل') + ' زر السلة لـ ' + response.data.count + ' منتج بنجاح');
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ في الاتصال');
                    },
                    complete: function() {
                        $button.text(originalText).prop('disabled', false);
                    }
                });
            }
        });
        </script>
        <?php
    }

    /**
     * معالجة الإجراءات الجماعية لحالة السلة
     */
    public function handle_bulk_cart_actions() {
        add_action('wp_ajax_bulk_update_cart_status', array($this, 'ajax_bulk_update_cart_status'));
    }

    /**
     * معالجة AJAX للإجراءات الجماعية
     */
    public function ajax_bulk_update_cart_status() {
        // التحقق من الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bulk_cart_nonce')) {
            wp_send_json_error('خطأ في التحقق من الأمان');
            return;
        }

        // التحقق من الصلاحيات
        if (!current_user_can('edit_products')) {
            wp_send_json_error('ليس لديك صلاحية لتعديل المنتجات');
            return;
        }

        $action = sanitize_text_field($_POST['cart_action']);
        $value = ($action === 'enable') ? 'yes' : 'no';

        // الحصول على جميع المنتجات
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'post_status' => 'publish'
        ));

        $count = 0;
        foreach ($products as $product) {
            update_post_meta($product->ID, '_form_elrakami_show_add_to_cart', $value);
            $count++;
        }

        wp_send_json_success(array('count' => $count));
    }

    /**
     * تفعيل زر السلة تلقائياً للمنتجات الجديدة
     */
    public function auto_enable_cart_for_new_products($post_id, $post, $update) {
        // التحقق من أن هذا منتج جديد وليس تحديث
        if ($post->post_type === 'product' && !$update) {
            // التحقق من الإعداد العام لتفعيل زر السلة افتراضياً
            $cart_default_enabled = get_option('form_elrakami_cart_default_enabled', 1);
            if ($cart_default_enabled) {
                // تفعيل زر السلة افتراضياً للمنتج الجديد
                update_post_meta($post_id, '_form_elrakami_show_add_to_cart', 'yes');
            }
        }
    }
}

// تهيئة الصنف
new Cart_Metabox();
