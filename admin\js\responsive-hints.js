/**
 * Responsive Hints JavaScript
 * 
 * Provides functionality for responsive design hints and preview in the form editor
 */

(function($) {
    'use strict';

    // Initialize responsive preview functionality
    function initResponsivePreview() {
        // Add responsive preview controls if they don't exist
        if ($('.responsive-preview-controls').length === 0) {
            const previewControls = `
                <div class="responsive-preview-controls">
                    <button type="button" class="preview-mobile" data-device="mobile">
                        <span class="dashicons dashicons-smartphone"></span> موبايل
                    </button>
                    <button type="button" class="preview-tablet" data-device="tablet">
                        <span class="dashicons dashicons-tablet"></span> تابلت
                    </button>
                    <button type="button" class="preview-desktop" data-device="desktop">
                        <span class="dashicons dashicons-desktop"></span> كمبيوتر
                    </button>
                </div>
            `;
            
            // Add preview controls before the form preview
            $('.form-preview-container').before(previewControls);
            
            // Set desktop as default active preview
            $('.preview-desktop').addClass('active');
            $('.form-preview-container').addClass('desktop');
        }
        
        // Handle preview device switching
        $('.responsive-preview-controls button').on('click', function() {
            const device = $(this).data('device');
            
            // Update active button
            $('.responsive-preview-controls button').removeClass('active');
            $(this).addClass('active');
            
            // Update preview container
            $('.form-preview-container').removeClass('mobile tablet desktop');
            $('.form-preview-container').addClass(device);
            
            // Trigger custom event for other scripts
            $(document).trigger('responsive_preview_changed', [device]);
        });
    }
    
    // Add responsive indicators to form fields
    function addResponsiveIndicators() {
        $('.form-field-item').each(function() {
            const fieldId = $(this).data('field-id');
            
            // Check if indicators already exist
            if ($(this).find('.field-responsive-status').length === 0) {
                const responsiveStatus = `
                    <div class="field-responsive-status">
                        <span class="device-visibility mobile visible" title="مرئي على الموبايل">M</span>
                        <span class="device-visibility tablet visible" title="مرئي على التابلت">T</span>
                        <span class="device-visibility desktop visible" title="مرئي على الكمبيوتر">D</span>
                    </div>
                `;
                
                $(this).find('.field-title').append(responsiveStatus);
            }
        });
        
        // Update indicators based on field settings
        updateResponsiveIndicators();
    }
    
    // Update responsive indicators based on field settings
    function updateResponsiveIndicators() {
        $('.form-field-item').each(function() {
            const fieldId = $(this).data('field-id');
            const fieldSettings = getFieldSettings(fieldId);
            
            if (fieldSettings) {
                // Update mobile visibility
                if (fieldSettings.hide_on_mobile) {
                    $(this).find('.device-visibility.mobile').removeClass('visible').addClass('hidden');
                } else {
                    $(this).find('.device-visibility.mobile').removeClass('hidden').addClass('visible');
                }
                
                // Update tablet visibility
                if (fieldSettings.hide_on_tablet) {
                    $(this).find('.device-visibility.tablet').removeClass('visible').addClass('hidden');
                } else {
                    $(this).find('.device-visibility.tablet').removeClass('hidden').addClass('visible');
                }
                
                // Update desktop visibility
                if (fieldSettings.hide_on_desktop) {
                    $(this).find('.device-visibility.desktop').removeClass('visible').addClass('hidden');
                } else {
                    $(this).find('.device-visibility.desktop').removeClass('hidden').addClass('visible');
                }
            }
        });
    }
    
    // Helper function to get field settings
    function getFieldSettings(fieldId) {
        // This would typically come from your form data
        // For now, return a placeholder
        return {
            hide_on_mobile: false,
            hide_on_tablet: false,
            hide_on_desktop: false
        };
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        // Only initialize on form editor page
        if ($('.form-editor-container').length > 0) {
            initResponsivePreview();
            addResponsiveIndicators();
            
            // Listen for field updates
            $(document).on('field_settings_updated', function(event, fieldId) {
                updateResponsiveIndicators();
            });
        }
    });

})(jQuery);
