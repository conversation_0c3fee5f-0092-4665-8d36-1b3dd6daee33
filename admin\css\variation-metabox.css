/**
 * أنماط CSS لميتا بوكس المتغيرات
 */

/* حقول المتغيرات */
.form-elrakami-variation-fields {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
    margin-top: 10px;
    border: 1px solid #e5e5e5;
}



/* وصف المتغير */
#variation_description {
    width: 100%;
    min-height: 80px;
}

/* تنسيق تبويب نموذج الرقمي */
#form_elrakami_product_data .options_group {
    padding: 0 12px;
}

#form_elrakami_product_data h3 {
    margin: 1.5em 0 1em;
    font-size: 1.1em;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5em;
}

#form_elrakami_product_data .description {
    margin-top: 0.5em;
    color: #777;
    font-style: italic;
}

/* تنسيق الحقول */
#form_elrakami_product_data .form-field {
    margin: 1em 0;
}

/* تنسيق الملاحظة */
#form_elrakami_product_data .form-field p.description {
    margin: 0.5em 0 1.5em;
    padding: 10px;
    background-color: #f8f8f8;
    border-right: 3px solid #2271b1;
    font-style: normal;
}

/* تنسيق قسم إعدادات عرض السمات */
.attribute-display-settings {
    margin: 1.5em 0;
}

.attribute-display-settings h4 {
    margin: 0 0 0.5em;
    font-size: 1em;
    font-weight: 600;
}

/* تنسيق جدول إعدادات السمات */
.attribute-display-table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.attribute-display-table th {
    background-color: #f8fafc;
    padding: 10px;
    text-align: right;
    font-weight: 600;
    border-bottom: 1px solid #e2e8f0;
}

.attribute-display-table td {
    padding: 10px;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.attribute-display-table tr:last-child td {
    border-bottom: none;
}

.attribute-display-table select {
    width: 100%;
    max-width: 250px;
}

/* تنسيق الأيقونات في الجدول */
.attribute-display-table .dashicons {
    margin-left: 5px;
    color: #2271b1;
}
