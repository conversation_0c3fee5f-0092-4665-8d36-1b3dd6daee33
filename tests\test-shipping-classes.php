<?php
/**
 * اختبارات فئات الشحن
 * 
 * هذا الملف يحتوي على اختبارات بسيطة للتأكد من عمل فئات الشحن بشكل صحيح
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * كلاس اختبار فئات الشحن
 */
class Form_Elrakami_Shipping_Classes_Test {

    /**
     * تشغيل جميع الاختبارات
     */
    public static function run_tests() {
        echo "<h2>اختبارات فئات الشحن - Form Elrakami</h2>";
        
        self::test_shipping_class_cost_calculation();
        self::test_shipping_class_settings();
        self::test_product_shipping_class_assignment();
        self::test_woocommerce_integration();
        
        echo "<h3>انتهت جميع الاختبارات</h3>";
    }

    /**
     * اختبار حساب تكلفة فئة الشحن
     */
    private static function test_shipping_class_cost_calculation() {
        echo "<h3>اختبار حساب تكلفة فئة الشحن</h3>";
        
        // إنشاء منتج تجريبي
        $product = new WC_Product_Simple();
        $product->set_name('منتج تجريبي');
        $product->set_price(100);
        $product->save();
        
        // إنشاء فئة شحن تجريبية
        $term = wp_insert_term('فئة تجريبية', 'product_shipping_class', array(
            'slug' => 'test-class'
        ));
        
        if (!is_wp_error($term)) {
            $class_id = $term['term_id'];
            
            // تعيين فئة الشحن للمنتج
            $product->set_shipping_class_id($class_id);
            $product->save();
            
            // تعيين تكلفة للفئة
            $shipping_class_costs = array($class_id => 200);
            update_option('form_elrakami_shipping_class_costs', $shipping_class_costs);
            
            // اختبار حساب التكلفة
            $woo_integration = new Form_Elrakami_Woo_Integration();
            $reflection = new ReflectionClass($woo_integration);
            $method = $reflection->getMethod('calculate_shipping_class_cost');
            $method->setAccessible(true);
            
            $cost = $method->invoke($woo_integration, $product);
            
            if ($cost == 200) {
                echo "<p style='color: green;'>✓ اختبار حساب التكلفة نجح: {$cost} دج</p>";
            } else {
                echo "<p style='color: red;'>✗ اختبار حساب التكلفة فشل: متوقع 200، حصلنا على {$cost}</p>";
            }
            
            // تنظيف
            wp_delete_term($class_id, 'product_shipping_class');
        } else {
            echo "<p style='color: red;'>✗ فشل في إنشاء فئة الشحن التجريبية</p>";
        }
        
        // حذف المنتج التجريبي
        $product->delete(true);
    }

    /**
     * اختبار إعدادات فئات الشحن
     */
    private static function test_shipping_class_settings() {
        echo "<h3>اختبار إعدادات فئات الشحن</h3>";
        
        // اختبار حفظ الإعدادات
        $test_settings = array(
            1 => 100,
            2 => 200,
            3 => 300
        );
        
        update_option('form_elrakami_shipping_class_costs', $test_settings);
        $saved_settings = get_option('form_elrakami_shipping_class_costs', array());
        
        if ($saved_settings === $test_settings) {
            echo "<p style='color: green;'>✓ اختبار حفظ الإعدادات نجح</p>";
        } else {
            echo "<p style='color: red;'>✗ اختبار حفظ الإعدادات فشل</p>";
        }
        
        // تنظيف
        delete_option('form_elrakami_shipping_class_costs');
    }

    /**
     * اختبار تعيين فئة الشحن للمنتج
     */
    private static function test_product_shipping_class_assignment() {
        echo "<h3>اختبار تعيين فئة الشحن للمنتج</h3>";
        
        // إنشاء منتج تجريبي
        $product = new WC_Product_Simple();
        $product->set_name('منتج تجريبي 2');
        $product->set_price(150);
        
        // إنشاء فئة شحن تجريبية
        $term = wp_insert_term('فئة تجريبية 2', 'product_shipping_class', array(
            'slug' => 'test-class-2'
        ));
        
        if (!is_wp_error($term)) {
            $class_id = $term['term_id'];
            
            // تعيين فئة الشحن للمنتج
            $product->set_shipping_class_id($class_id);
            $product->save();
            
            // التحقق من التعيين
            $assigned_class_id = $product->get_shipping_class_id();
            
            if ($assigned_class_id == $class_id) {
                echo "<p style='color: green;'>✓ اختبار تعيين فئة الشحن نجح</p>";
            } else {
                echo "<p style='color: red;'>✗ اختبار تعيين فئة الشحن فشل</p>";
            }
            
            // تنظيف
            wp_delete_term($class_id, 'product_shipping_class');
        } else {
            echo "<p style='color: red;'>✗ فشل في إنشاء فئة الشحن التجريبية</p>";
        }
        
        // حذف المنتج التجريبي
        $product->delete(true);
    }

    /**
     * اختبار تكامل WooCommerce
     */
    private static function test_woocommerce_integration() {
        echo "<h3>اختبار تكامل WooCommerce</h3>";
        
        // التحقق من وجود فئات الشحن في WooCommerce
        $shipping_classes = WC()->shipping()->get_shipping_classes();
        
        if (is_array($shipping_classes)) {
            echo "<p style='color: green;'>✓ تم العثور على " . count($shipping_classes) . " فئة شحن في WooCommerce</p>";
        } else {
            echo "<p style='color: red;'>✗ فشل في الحصول على فئات الشحن من WooCommerce</p>";
        }
        
        // التحقق من وجود كلاس التكامل
        if (class_exists('Form_Elrakami_Woo_Integration')) {
            echo "<p style='color: green;'>✓ كلاس التكامل موجود</p>";
        } else {
            echo "<p style='color: red;'>✗ كلاس التكامل غير موجود</p>";
        }
        
        // التحقق من وجود كلاس إدارة مناطق الشحن
        if (class_exists('Form_Elrakami_Shipping_Zones_Manager')) {
            echo "<p style='color: green;'>✓ كلاس إدارة مناطق الشحن موجود</p>";
        } else {
            echo "<p style='color: red;'>✗ كلاس إدارة مناطق الشحن غير موجود</p>";
        }
    }
}

// تشغيل الاختبارات إذا تم طلبها
if (isset($_GET['run_shipping_classes_tests']) && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-info"><div style="padding: 20px;">';
        Form_Elrakami_Shipping_Classes_Test::run_tests();
        echo '</div></div>';
    });
}
