/**
 * تنسيقات مخصصة لتوحيد ارتفاع الحقول مع مربعات الأيقونات
 * هذه التنسيقات تساعد على حل مشكلة عدم تناسق ارتفاع الحقول
 * تم تعديلها لإزالة اعتماد Select2
 */

/* توحيد ارتفاع جميع الحقول لتتناسب مع ارتفاع مربعات الأيقونات */
.form-elrakami-field input[type="text"],
.form-elrakami-field input[type="tel"],
.form-elrakami-field input[type="email"],
.form-elrakami-field input[type="number"],
.form-elrakami-field select {
    height: 38px !important;
    box-sizing: border-box !important;
}

/* ضبط مربعات الأيقونات لتكون متناسقة */
.form-elrakami-field .field-icon {
    width: 38px !important;
    height: 40px !important;
    line-height: 40px !important;
}

/* تصحيح المحاذاة العمودية للنص داخل الحقول */
.form-elrakami-field input[type="text"],
.form-elrakami-field input[type="tel"],
.form-elrakami-field input[type="email"],
.form-elrakami-field input[type="number"] {
    line-height: 38px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

/* تصحيح ارتفاع حقول الولاية والبلدية */
.form-elrakami-field[data-field-type="state"] select,
.form-elrakami-field[data-field-type="city"] select,
.form-elrakami-field select[name="state"],
.form-elrakami-field select[name="municipality"] {
    height: 38px !important;
    width: 100% !important;
    margin: 0 !important;
    border-color: var(--form-input-border, #e2e8f0) !important;
    background-color: var(--form-input-bg, #f8fafc) !important;
    border-radius: var(--form-input-radius, 4px) !important;
    color: var(--form-text-color, #1e293b) !important;
    padding: 0 0.75rem !important;
    -webkit-appearance: menulist;
    -moz-appearance: menulist;
    appearance: menulist;
}

/* تطبيق التنسيق على القوائم المنسدلة داخل مجموعات الإدخال */
.input-group select {
    border-color: var(--form-input-border, #e2e8f0) !important;
    background-color: var(--form-input-bg, #f8fafc) !important;
    color: var(--form-text-color, #1e293b) !important;
    height: 38px !important;
    width: 100% !important;
}

/* تخصيص CSS للقوائم المنسدلة داخل مجموعات الإدخال */
.form-elrakami-container.icons-right .input-group select {
    border-radius: var(--form-input-radius) 0 0 var(--form-input-radius) !important;
}

.form-elrakami-container.icons-left .input-group select {
    border-radius: 0 var(--form-input-radius) var(--form-input-radius) 0 !important;
}

/* تحسين مظهر التركيز على الحقول */
.form-elrakami-field input:focus,
.form-elrakami-field select:focus {
    border-color: #3498db !important;
    box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.3) !important;
}

/* تحسين المظهر على أجهزة الجوال */
@media (max-width: 768px) {
    .form-elrakami-field input[type="text"],
    .form-elrakami-field input[type="tel"],
    .form-elrakami-field input[type="email"],
    .form-elrakami-field input[type="number"],
    .form-elrakami-field select {
        height: 38px !important;
    }
    
    .form-elrakami-field .field-icon {
        height: 40px !important;
        line-height: 40px !important;
    }
}

/* تصحيح مظهر الحقول مع التخطيطات المختلفة */
.form-elrakami-form.columns-layout .form-elrakami-field input,
.form-elrakami-form.columns-layout .form-elrakami-field select {
    width: 100% !important;
}

/* تحسين ارتفاع منطقة النص لتتناسب مع الأيقونات */
.form-elrakami-field textarea {
    min-height: 80px !important;
    padding-top: 8px !important;
}

/* تخصيص قوائم الولاية والبلدية لتحسين المظهر والسلوك */
.form-elrakami-field option {
    padding: 8px 12px !important;
    color: var(--form-text-color, #1e293b) !important;
}

/* تحسين مظهر العناصر المعطلة */
.form-elrakami-field select:disabled {
    background-color: #f1f1f1 !important;
    color: #888 !important;
    cursor: not-allowed !important;
}

/* تخصيص حالة التحويم على القوائم المنسدلة */
.form-elrakami-field select:hover,
.input-group select:hover {
    border-color: var(--form-primary-color, #3498db) !important;
}