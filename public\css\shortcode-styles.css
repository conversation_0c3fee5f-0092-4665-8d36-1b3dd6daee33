/**
 * أنماط خاصة بالشورت كود
 * تحسين عرض النموذج عند استخدامه في الصفحات عبر الشورت كود
 */

/* حاوي النموذج في الشورت كود */
.form-elrakami-shortcode-container {
    max-width: 800px !important;
    margin: 20px auto !important;
    padding: 20px !important;
    background: #fff !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e0e0e0 !important;
    position: relative !important;
    overflow: hidden !important;
}

/* تحسين العرض على الشاشات الصغيرة */
@media (max-width: 768px) {
    .form-elrakami-shortcode-container {
        max-width: 95% !important;
        margin: 10px auto !important;
        padding: 15px !important;
        border-radius: 8px !important;
    }
}

@media (max-width: 480px) {
    .form-elrakami-shortcode-container {
        max-width: 100% !important;
        margin: 5px auto !important;
        padding: 10px !important;
        border-radius: 6px !important;
    }
}

/* تحسين عرض معلومات المنتج في الشورت كود */
.form-elrakami-shortcode-container .product-info-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 25px !important;
    border: 1px solid #dee2e6 !important;
}

.form-elrakami-shortcode-container .product-image-container {
    text-align: center !important;
    margin-bottom: 15px !important;
}

.form-elrakami-shortcode-container .product-image {
    max-width: 200px !important;
    max-height: 200px !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

@media (max-width: 768px) {
    .form-elrakami-shortcode-container .product-image {
        max-width: 150px !important;
        max-height: 150px !important;
    }
}

@media (max-width: 480px) {
    .form-elrakami-shortcode-container .product-image {
        max-width: 120px !important;
        max-height: 120px !important;
    }
}

/* تحسين عرض تفاصيل المنتج */
.form-elrakami-shortcode-container .product-details {
    text-align: center !important;
}

.form-elrakami-shortcode-container .product-title {
    font-size: 1.5em !important;
    font-weight: bold !important;
    color: #333 !important;
    margin-bottom: 10px !important;
    line-height: 1.4 !important;
}

.form-elrakami-shortcode-container .product-price {
    font-size: 1.3em !important;
    font-weight: bold !important;
    color: var(--form-primary-color, #007cba) !important;
    margin-bottom: 15px !important;
}

@media (max-width: 480px) {
    .form-elrakami-shortcode-container .product-title {
        font-size: 1.2em !important;
    }

    .form-elrakami-shortcode-container .product-price {
        font-size: 1.1em !important;
    }
}

/* تحسين النموذج داخل الشورت كود */
.form-elrakami-shortcode-container .form-elrakami-form {
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* تحسين الحقول */
.form-elrakami-shortcode-container .form-field {
    margin-bottom: 20px !important;
}

.form-elrakami-shortcode-container .form-field input,
.form-elrakami-shortcode-container .form-field select,
.form-elrakami-shortcode-container .form-field textarea {
    width: 100% !important;
    padding: 12px 15px !important;
    border: 2px solid #e0e0e0 !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
}

/* إزالة قوية جداً للسهم من القوائم المنسدلة في الشورت كود */
.form-elrakami-shortcode-container .form-field select,
.form-elrakami-shortcode-container select,
.form-elrakami-shortcode-container .form-field select:focus,
.form-elrakami-shortcode-container select:focus,
.form-elrakami-shortcode-container .form-field select:hover,
.form-elrakami-shortcode-container select:hover,
.form-elrakami-shortcode-container .form-field select:active,
.form-elrakami-shortcode-container select:active {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: #fff !important;
    border-radius: 8px !important;
}

/* إزالة السهم من متصفح Internet Explorer و Edge */
.form-elrakami-shortcode-container select::-ms-expand,
.form-elrakami-shortcode-container .form-field select::-ms-expand {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* إزالة السهم من جميع عناصر select في الشورت كود بغض النظر عن التداخل */
.form-elrakami-shortcode-container * select,
.form-elrakami-shortcode-wrapper * select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

.form-elrakami-shortcode-container .form-field input:focus,
.form-elrakami-shortcode-container .form-field select:focus,
.form-elrakami-shortcode-container .form-field textarea:focus {
    border-color: var(--form-primary-color, #007cba) !important;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1) !important;
    outline: none !important;
}

/* تحسين أزرار الكمية */
.form-elrakami-shortcode-container .quantity-controls {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 15px !important;
    margin: 20px 0 !important;
}

.form-elrakami-shortcode-container .quantity-btn {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    border: 2px solid var(--form-primary-color, #007cba) !important;
    background: white !important;
    color: var(--form-primary-color, #007cba) !important;
    font-size: 18px !important;
    font-weight: bold !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.form-elrakami-shortcode-container .quantity-btn:hover {
    background: var(--form-primary-color, #007cba) !important;
    color: white !important;
}

.form-elrakami-shortcode-container .quantity-display {
    font-size: 1.2em !important;
    font-weight: bold !important;
    min-width: 50px !important;
    text-align: center !important;
}

/* تحسين ملخص الطلب */
.form-elrakami-shortcode-container .order-summary {
    background: #f8f9fa !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin: 25px 0 !important;
    border: 1px solid #dee2e6 !important;
}

.form-elrakami-shortcode-container .order-summary h3 {
    margin-top: 0 !important;
    margin-bottom: 15px !important;
    color: #333 !important;
    font-size: 1.2em !important;
}

/* تحسين زر الطلب */
.form-elrakami-shortcode-container .submit-button {
    width: 100% !important;
    padding: 20px 35px !important;
    font-size: 1.2em !important;
    font-weight: bold !important;
    border-radius: 8px !important;
    border: none !important;
    background: linear-gradient(135deg, var(--form-primary-color, #007cba) 0%, #005a87 100%) !important;
    color: white !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin-top: 20px !important;
    min-height: 65px !important;
}

.form-elrakami-shortcode-container .submit-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 124, 186, 0.3) !important;
}

.form-elrakami-shortcode-container .submit-button:active {
    transform: translateY(0) !important;
}

/* تحسين الرسائل */
.form-elrakami-shortcode-container .form-message {
    padding: 15px !important;
    border-radius: 8px !important;
    margin: 15px 0 !important;
    font-weight: 500 !important;
}

.form-elrakami-shortcode-container .form-message.success {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

.form-elrakami-shortcode-container .form-message.error {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

/* تحسين التحميل */
.form-elrakami-shortcode-container .loading-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1000 !important;
    border-radius: 12px !important;
}

.form-elrakami-shortcode-container .loading-spinner {
    width: 40px !important;
    height: 40px !important;
    border: 4px solid #f3f3f3 !important;
    border-top: 4px solid var(--form-primary-color, #007cba) !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الشريط السفلي في الشورت كود */
.form-elrakami-shortcode-wrapper .form-elrakami-sticky-bar {
    /* لا نخفي الشريط السفلي في الشورت كود، بل نتركه يعمل حسب الإعدادات */
    z-index: 999999 !important;
}

/* تحسين عرض الشريط السفلي للشورت كود على الشاشات الصغيرة */
@media (max-width: 768px) {
    .form-elrakami-shortcode-wrapper .form-elrakami-sticky-bar {
        padding: 8px 15px !important;
    }

    .form-elrakami-shortcode-wrapper .form-elrakami-sticky-bar .form-elrakami-sticky-bar-product {
        gap: 8px !important;
    }

    .form-elrakami-shortcode-wrapper .form-elrakami-sticky-bar .form-elrakami-sticky-bar-product img {
        width: 40px !important;
        height: 40px !important;
    }
}

/* تحسين الاستجابة للشاشات الصغيرة جداً */
@media (max-width: 360px) {
    .form-elrakami-shortcode-container {
        padding: 8px !important;
    }

    .form-elrakami-shortcode-container .product-info-section {
        padding: 15px !important;
    }

    .form-elrakami-shortcode-container .form-field input,
    .form-elrakami-shortcode-container .form-field select,
    .form-elrakami-shortcode-container .form-field textarea {
        padding: 10px 12px !important;
        font-size: 14px !important;
    }

    .form-elrakami-shortcode-container .quantity-btn {
        width: 35px !important;
        height: 35px !important;
        font-size: 16px !important;
    }

    .form-elrakami-shortcode-container .submit-button {
        padding: 18px 25px !important;
        font-size: 1.1em !important;
        min-height: 60px !important;
    }
}
