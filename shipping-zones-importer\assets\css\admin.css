 .card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.card h2, .card h3 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.card h3 {
    margin-top: 30px;
    font-size: 1.2em;
}

.shipping-companies {
    margin: 20px 0;
}

.company-item {
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 6px;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.company-item:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.company-item.disabled {
    opacity: 0.8;
    background: #f8f9fa;
    border-color: #eee;
}

.company-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.button-danger {
    color: #fff !important;
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    transition: all 0.2s ease;
}

.button-danger:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
}

.toggle-company, .delete-company {
    min-width: 80px;
    text-align: center;
    font-weight: 500;
}

.import-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
    color: #155724 !important;
    padding: 15px !important;
    border-radius: 4px;
}

.import-error {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
    color: #721c24 !important;
    padding: 15px !important;
    border-radius: 4px;
}

#import-progress {
    margin: 15px 0;
    padding: 20px;
    border-radius: 6px;
    display: none;
    font-weight: 500;
    line-height: 1.6;
}

#import-progress hr {
    border: none;
    border-top: 1px solid rgba(0,0,0,0.1);
    margin: 15px 0;
}

#import-progress strong {
    display: block;
    margin-top: 15px;
    color: #2271b1;
}

#import-progress .importing {
    text-align: center;
    padding: 20px;
    background: rgba(0,0,0,0.03);
    border-radius: 4px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* تحسينات للرسائل */
.import-success,
.import-error {
    margin: 15px 0 !important;
    padding: 20px !important;
    border-radius: 6px !important;
    border-width: 1px !important;
    border-style: solid !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.importing {
    color: #2271b1;
    font-weight: 500;
}

.company-header h3 {
    margin: 0;
    padding: 0;
    border: none;
    font-size: 1.1em;
}

.company-actions {
    display: flex;
    gap: 10px;
}

.company-status {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
    font-size: 0.9em;
    color: #666;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ccc;
}

.company-item:not(.disabled) .status-indicator {
    background: #46b450;
}

.company-item .notice {
    margin: 10px 0 5px;
}

#add-company-form {
    margin: 20px 0;
}

#add-company-form input[type="text"] {
    width: 250px;
    margin-left: 10px;
    margin-bottom: 10px;
}

#pricing-upload-form {
    margin: 20px 0;
}

.form-group {
    margin-bottom: 15px;
}

#pricing-upload-form select {
    width: 250px;
    margin-bottom: 5px;
    display: block;
}

#pricing_file {
    display: inline-block;
    margin-left: 10px;
    margin-bottom: 0;
    vertical-align: middle;
}

body.rtl #pricing_file {
    margin-left: 0;
    margin-right: 10px;
}

#pricing-upload-form .button-primary {
    vertical-align: middle;
}

.description {
    color: #666;
    font-style: italic;
    margin: 5px 0 15px;
}

#import-progress {
    margin-top: 20px;
    padding: 10px;
    background: #f8f9fa;
    border-right: 4px solid #0073aa;
    display: none;
}

.import-success {
    border-right-color: #46b450 !important;
}

.import-error {
    border-right-color: #dc3232 !important;
}

#import-shipping-zones {
    margin-top: 10px;
}

/* تحسينات للغة العربية */
body.rtl .card {
    text-align: right;
}

body.rtl .description {
    text-align: right;
}

body.rtl #import-progress {
    border-right: none;
    border-left: 4px solid #0073aa;
}

body.rtl .import-success {
    border-left-color: #46b450 !important;
    border-right: none !important;
}

body.rtl .import-error {
    border-left-color: #dc3232 !important;
    border-right: none !important;
}

body.rtl #add-company-form input[type="text"] {
    margin-left: 0;
    margin-right: 10px;
}

body.rtl .company-item {
    text-align: right;
}