# دليل استخدام فئات الشحن في إضافة Form Elrakami

## ما هي فئات الشحن؟

فئات الشحن (Shipping Classes) هي ميزة في WooCommerce تسمح لك بتجميع المنتجات حسب خصائص الشحن المشتركة وتطبيق تكاليف شحن مختلفة على كل فئة.

## كيف تعمل فئات الشحن في إضافة Form Elrakami؟

### المفهوم الأساسي:
- **التكلفة الأساسية**: تكلفة الشحن العادية للولاية (مثل 500 دج لولاية الجزائر)
- **تكلفة الفئة**: التكلفة الإضافية للمنتجات من فئة معينة (مثل 200 دج للمنتجات الثقيلة)
- **التكلفة النهائية**: التكلفة الأساسية + تكلفة الفئة = 500 + 200 = 700 دج

### مثال عملي:
إذا كان لديك:
- سعر التوصيل لولاية الجزائر: 500 دج
- فئة "المنتجات الثقيلة" بتكلفة إضافية: 200 دج
- فئة "المنتجات الهشة" بتكلفة إضافية: 150 دج

النتيجة:
- منتج عادي: 500 دج
- منتج ثقيل: 700 دج (500 + 200)
- منتج هش: 650 دج (500 + 150)

## خطوات الإعداد

### 1. إنشاء فئات الشحن في WooCommerce

1. اذهب إلى **WooCommerce > الإعدادات > الشحن > الفئات**
2. أضف فئة جديدة:
   - **اسم الفئة**: مثل "المنتجات الثقيلة"
   - **الرمز**: مثل "heavy-products"
   - **الوصف**: وصف اختياري للفئة
3. كرر العملية لإضافة فئات أخرى حسب الحاجة

### 2. تحديد التكاليف الإضافية في إضافة Form Elrakami

1. اذهب إلى **Form Elrakami > إدارة مناطق الشحن**
2. انتقل إلى قسم **"إعدادات فئات الشحن"**
3. حدد التكلفة الإضافية لكل فئة بالدينار الجزائري
4. اضغط **"حفظ إعدادات فئات الشحن"**

### 3. تطبيق الفئات على المنتجات

1. اذهب إلى **المنتجات > تحرير منتج**
2. في قسم **"بيانات المنتج"** > تبويب **"الشحن"**
3. اختر الفئة المناسبة من قائمة **"فئة الشحن"**
4. احفظ المنتج

### 4. إعادة استيراد مناطق الشحن (مهم!)

بعد إعداد فئات الشحن وتكاليفها:
1. ارجع إلى **Form Elrakami > إدارة مناطق الشحن**
2. اضغط **"استيراد مناطق الشحن"** لتطبيق الإعدادات الجديدة

## أمثلة على فئات الشحن الشائعة

### فئات حسب الوزن:
- **المنتجات الخفيفة**: 0 دج إضافية
- **المنتجات المتوسطة**: 100 دج إضافية
- **المنتجات الثقيلة**: 200 دج إضافية

### فئات حسب الحجم:
- **المنتجات الصغيرة**: 0 دج إضافية
- **المنتجات الكبيرة**: 150 دج إضافية
- **المنتجات الضخمة**: 300 دج إضافية

### فئات حسب نوع المنتج:
- **المنتجات العادية**: 0 دج إضافية
- **المنتجات الهشة**: 100 دج إضافية
- **المنتجات الخطرة**: 250 دج إضافية
- **الأجهزة الإلكترونية**: 200 دج إضافية

## نصائح مهمة

### 1. التخطيط المسبق
- خطط لفئات الشحن قبل إضافة المنتجات
- استخدم أسماء واضحة ومفهومة للفئات
- لا تفرط في عدد الفئات لتجنب التعقيد

### 2. اختبار النظام
- اختبر حساب تكاليف الشحن بعد الإعداد
- تأكد من أن التكاليف تظهر بشكل صحيح في النموذج
- اختبر مع منتجات من فئات مختلفة

### 3. التحديث والصيانة
- راجع فئات الشحن وتكاليفها بانتظام
- حدث التكاليف حسب تغيرات أسعار الشحن
- احذف الفئات غير المستخدمة

## استكشاف الأخطاء

### المشكلة: لا تظهر فئات الشحن في الإعدادات
**الحل**: تأكد من إنشاء فئات الشحن في WooCommerce أولاً

### المشكلة: التكلفة الإضافية لا تظهر في النموذج
**الحل**: 
1. تأكد من حفظ إعدادات فئات الشحن
2. تأكد من تطبيق الفئة على المنتج
3. أعد استيراد مناطق الشحن

### المشكلة: التكلفة غير صحيحة
**الحل**:
1. تحقق من إعدادات الفئة في Form Elrakami
2. تحقق من أن المنتج مرتبط بالفئة الصحيحة
3. راجع سجلات الأخطاء في WordPress

## الدعم الفني

إذا واجهت أي مشاكل في استخدام فئات الشحن، يمكنك:
1. مراجعة سجلات الأخطاء في WordPress
2. التحقق من إعدادات WooCommerce
3. التواصل مع فريق الدعم الفني

---

**ملاحظة**: هذه الميزة تتطلب WooCommerce 3.0 أو أحدث وتعمل مع جميع طرق الشحن المدعومة في الإضافة.
